.zhuige-waterfall {
    align-content: flex-start;
    align-items: flex-start;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    padding: 0 10rpx 20rpx;
}

.zhuige-waterfall-list {
    box-sizing: border-box;
    padding: 0 10rpx;
    width: 50%;
}

.zhuige-waterfall-block {
    background-color: #fff;
    border-radius: 12rpx;
    color: #555;
    font-size: 28rpx;
    margin-bottom: 20rpx;
}

.zhuige-waterfall-img {
    position: relative;
}

.zhuige-waterfall-img image {
    border-radius: 12rpx 12rpx 0 0;
    display: inherit;
    height: 200rpx;
    width: 100%;
}

.waterfall-mark {
    background: #ff6146;
    border-radius: 6rpx 0 6rpx 0;
    color: #fff;
    font-size: 22rpx;
    height: 48rpx;
    left: 0;
    line-height: 48rpx;
    padding: 0 24rpx;
    position: absolute;
    top: 0;
    z-index: 3;
}

.zhuige-waterfall-footer {
    justify-content: space-between;
    padding: 0 16rpx 10rpx;
}

.zhuige-waterfall-footer,.zhuige-waterfall-user {
    align-items: center;
    display: flex;
}

.zhuige-waterfall-user image {
    border-radius: 50%;
    height: 48rpx;
    margin-right: 12rpx;
    width: 48rpx;
}

.zhuige-waterfall-user text {
    color: #333;
    font-size: 24rpx;
    font-weight: 400;
}

.zhuige-waterfall-text {
    line-height: 1em;
    margin-top: 12rpx;
    padding: 12rpx 16rpx 0;
}

.zhuige-waterfall-text view {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.zhuige-waterfall-text view.title {
    color: #333;
    font-size: 30rpx;
    font-weight: 600;
}

.zhuige-waterfall-text view.excerpt {
    color: #555;
    font-size: 26rpx;
    font-weight: 400;
    margin-top: 26rpx;
}

.zhuige-waterfall-tags {
    display: flex;
    flex-wrap: wrap;
    height: 40rpx;
    margin: 26rpx 0 0;
    overflow: hidden;
    padding: 0 16rpx;
}

.zhuige-waterfall-tags text {
    background: #f5f5f5;
    border-radius: 6rpx;
    color: #333;
    font-size: 20rpx;
    font-weight: 400;
    height: 36rpx;
    line-height: 36rpx;
    margin-right: 8rpx;
    padding: 0 12rpx;
}

.zhuige-waterfall-tags text:nth-child(1) {
    background: #2c70db;
    color: #fff;
}

.zhuige-waterfall-price {
    align-items: center;
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
}

.zhuige-waterfall-price text:nth-child(1) {
    color: #333;
    font-size: 24rpx;
    font-weight: 300;
}

.zhuige-waterfall-price text:nth-child(2) {
    color: #333;
    font-size: 36rpx;
    font-weight: 600;
    margin-left: 8rpx;
}

.zhuige-waterfall-price .zhuige-waterfall-vip-price {
    border: 1rpx solid #ff6146;
    border-radius: 5rpx;
    color: #ff6146;
    font-size: 18rpx;
    height: 1.4em;
    line-height: 1.4em;
    margin-left: 12rpx;
    padding: 0 3px;
}

.zhuige-waterfall-price .zhuige-waterfall-orig-price {
    color: #999;
    font-size: 24rpx;
    font-weight: 300;
    margin-left: 16rpx;
    text-decoration: line-through;
}

.zhuige-waterfall-list-ad {
    margin-bottom: 20rpx;
}

.zhuige-idle-header {
    left: 0;
    position: fixed;
    width: 100%;
    z-index: 9;
}

.zhuige-tab-wide-box .zhuige-tab,.zhuige-tab-wide-box .zhuige-tab-opt {
    background: none!important;
}

.zhuige-tab-wide-box .zhuige-tab-nav {
    width: 92%;
}

.zhuige-swiper-box {
    padding: 280rpx 20rpx 20rpx;
}

.zhuige-top-bar {
    left: 10rpx;
}

.zhuige-tab-box:first-of-type {
    margin-left: -24rpx!important;
}

.zhuge-y_pages-tab {
    align-items: center;
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    bottom: 0rpx;
    box-shadow: 0 -20rpx 32rpx -18rpx rgba(79,125,183,.3);
    display: flex;
    flex-wrap: nowrap;
    height: 168rpx;
    padding: 0 4%;
    position: fixed;
    width: 92%;
    z-index: 999;
}

.zhuge-y_pages-tab-icon {
    margin-bottom: 20rpx;
    text-align: center;
    width: 18%;
}

.res-tab2 .zhuge-y_pages-tab-icon {
    width: 50%;
}

.res-tab3 .zhuge-cloumn-tab-icon {
    width: 40%;
}

.res-tab4 .zhuge-y_pages-tab-icon {
    width: 25%;
}

.res-tab5 .zhuge-y_pages-tab-icon {
    width: 18%;
}

.zhuge-y_pages-tab-icon:nth-child(1) {
    background: #fff;
    border-radius: 160rpx;
    height: 160rpx;
    margin-top: -48rpx;
    width: 160rpx;
}

.zhuge-y_pages-tab-icon image {
    margin: 4rpx auto;
}

.zhuge-y_pages-tab-icon:nth-child(1) image {
    height: 96rpx;
    margin: 24rpx auto 4rpx;
    width: 96rpx;
}

.zhuge-y_pages-tab-icon image {
    height: 64rpx;
    margin: -8rpx auto 0;
    width: 64rpx;
}

.zhuge-y_pages-tab-icon view {
    color: #666;
    font-size: 24rpx;
    font-weight: 400;
    height: 1em;
    line-height: 1em;
    margin-top: -16rpx;
}

.uni-navbar--border {
    border: none!important;
}

.top-input {
    margin-left: 8rpx;
}

.top-input-placeholder,.zhuige-scroll-move .top-input-placeholder {
    color: #999;
}

.zhuige-waterfall-list-ad .zhuige-dots-left .wx-swiper-dots.wx-swiper-dots-horizontal {
    bottom: 30rpx;
}

.zhuige-waterfall-list-ad .zhuige-swiper .zhuige-swiper-block .zhuige-swiper-title {
    bottom: 56rpx;
    font-size: 30rpx;
    left: 12rpx;
}
