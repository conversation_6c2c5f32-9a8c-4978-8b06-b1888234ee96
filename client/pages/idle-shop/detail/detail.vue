<template>
	<view class="content">
		<!-- 商品图片轮播 -->
		<view class="zhuige-swiper-box">
			<zhuige-swiper :items="goods.images" :dots="true"></zhuige-swiper>
		</view>
		
		<!-- 商品信息 -->
		<view class="zhuige-goods-info">
			<view class="zhuige-goods-price">
				<text>￥</text>
				<text>{{ goods.price }}</text>
			</view>
			<view class="zhuige-goods-title">{{ goods.title }}</view>
			<view class="zhuige-goods-tags" v-if="goods.tags && goods.tags.length > 0">
				<text v-for="(tag, index) in goods.tags" :key="index">{{ tag.name }}</text>
			</view>
		</view>
		
		<!-- 用户信息 -->
		<view class="zhuige-user-info" @click="clickUser(goods.author.user_id)">
			<view class="zhuige-user-avatar">
				<image :src="goods.author.avatar" mode="aspectFill" />
				<view class="zhuige-user-certify" v-if="goods.author.certify">
					<image :src="goods.author.certify.icon" mode="aspectFill" />
				</view>
			</view>
			<view class="zhuige-user-detail">
				<view class="zhuige-user-name">{{ goods.author.nickname }}</view>
				<view class="zhuige-user-sign" v-if="goods.author.sign">{{ goods.author.sign }}</view>
			</view>
			<view class="zhuige-user-follow" @click.stop="clickFollowUser(goods.author.user_id)">
				<text v-if="goods.author.is_follow">已关注</text>
				<text v-else>关注</text>
			</view>
		</view>
		
		<!-- 商品详情 -->
		<view class="zhuige-goods-content">
			<view class="zhuige-goods-content-title">商品详情</view>
			<view class="zhuige-goods-content-text">
				<mp-html :content="goods.content" @link="onMPHtmlLink" />
			</view>
		</view>
		
		<!-- 推荐分类 -->
		<view class="zhuige-rec-cat" v-if="goods.rec_cat" @click="clickRecCat">
			<view>推荐分类</view>
			<view>
				<text>{{ goods.rec_cat.name }}</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
		</view>
		
		<!-- 评论区域 -->
		<view class="zhuige-comment-box" v-if="goods.comment_switch">
			<view class="zhuige-comment-title">
				<text>评论 {{ goods.comment_count }}</text>
				<text @click="clickAllComments">查看全部</text>
			</view>
			<view class="zhuige-comment-list">
				<view 
					class="zhuige-comment-item" 
					v-for="(comment, index) in comments" 
					:key="index"
				>
					<view class="zhuige-comment-user" @click="clickUser(comment.user_id)">
						<image :src="comment.avatar" mode="aspectFill" />
						<text>{{ comment.nickname }}</text>
					</view>
					<view class="zhuige-comment-content">{{ comment.content }}</view>
					<view class="zhuige-comment-time">
						<text>{{ comment.time }}</text>
						<text @click="clickReply(comment)">回复</text>
					</view>
				</view>
			</view>
			<view class="zhuige-comment-more" v-if="comments.length > 0" @click="clickAllComments">
				查看更多评论
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="zhuige-bottom-bar">
			<view class="zhuige-bottom-left">
				<view @click="openComment(0)">
					<uni-icons type="chat" size="24" color="#666"></uni-icons>
					<text>评论</text>
				</view>
				<view @click="clickPromotion" v-if="goods.author.user_id == user_id">
					<uni-icons type="star" size="24" color="#666"></uni-icons>
					<text>推广</text>
				</view>
				<view @click="clickEdit" v-if="goods.author.user_id == user_id">
					<uni-icons type="compose" size="24" color="#666"></uni-icons>
					<text>编辑</text>
				</view>
			</view>
			<view class="zhuige-bottom-right">
				<view class="zhuige-btn-sell" @click="clickSell">卖同款</view>
				<view class="zhuige-btn-buy" @click="clickBuy">我想要</view>
			</view>
		</view>
		
		<!-- 二维码弹窗 -->
		<uni-popup ref="popupQrcode" type="center">
			<view class="zhuige-pop-qr">
				<view>
					<image :src="qrcode" mode="aspectFill" :show-menu-by-longpress="true" />
				</view>
				<view>扫一扫/长按加好友</view>
			</view>
		</uni-popup>
		
		<!-- 评论弹窗 -->
		<uni-popup ref="popupComment" type="bottom">
			<view class="zhuige-comment-popup">
				<view class="zhuige-comment-input">
					<input 
						v-model="comment_content" 
						placeholder="说点什么..." 
						:focus="comment_focus"
						@blur="comment_focus = false"
					/>
					<view class="zhuige-comment-send" @click="sendComment">发送</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	components: {
		ZhuigeSwiper: () => import('@/components/zhuige-swiper'),
		MpHtml: () => import('@/components/mp-html/mp-html')
	},
	data() {
		return {
			goods_id: 0,
			goods: {
				images: [],
				tags: [],
				author: {},
				rec_cat: null
			},
			comments: [],
			loadMore: 'more',
			user_id: 0,
			qrcode: '',
			comment_content: '',
			comment_focus: false,
			parent_comment_id: 0,
			reply_user_id: 0,
			loginReload: false
		}
	},
	onLoad(options) {
		this.goods_id = parseInt(options.id)
		this.user_id = getApp().globalData.user_id
		this.loadGoods()
	},
	onShow() {
		if (this.loginReload) {
			this.loginReload = false
			this.loadGoods()
		}
	},
	onShareAppMessage() {
		return {
			title: this.goods.title + '-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/detail/detail?id=' + this.goods_id + '&n=n'),
			imageUrl: this.goods.thumb
		}
	},
	onShareTimeline() {
		return {
			title: this.goods.title + '-' + getApp().globalData.appName
		}
	},
	methods: {
		onSetReload() {
			this.loginReload = true
		},
		onMPHtmlLink(e) {
			if (e['data-link']) {
				Share.openLink(e['data-link'])
			}
		},
		clickLink(url) {
			Share.openLink(url)
		},
		clickPromotion() {
			Share.openLink('/pages/promotion/pay/pay?id=' + this.goods_id)
		},
		clickEdit() {
			Share.openLink('/pages/idle-shop/post/post?goods_id=' + this.goods_id)
		},
		clickRecCat() {
			Share.openLink('/pages/idle-shop/list/list?title=分类【' + this.goods.rec_cat.name + '】&cat_id=' + this.goods.rec_cat.id)
		},
		clickSell() {
			let url = '/pages/idle-shop/post/post'
			if (this.goods.rec_cat) {
				url += '?cat_id=' + this.goods.rec_cat.id
			}
			Share.openLink(url)
		},
		clickBuy() {
			if (this.goods.author.weixin) {
				this.qrcode = this.goods.author.weixin
				this.$refs.popupQrcode.open('center')
			} else {
				Tools.toast('该用户未设置微信二维码')
			}
		},
		checkComment() {
			if (!this.goods.comment_switch) {
				Tools.error('评论已关闭')
				return false
			}
			if (this.goods.comment_require_mobile2) {
				Share.openLink('/pages/user/login/login?type=mobile&tip=评论')
				return false
			}
			if (this.goods.comment_require_avatar2) {
				Share.openLink('/pages/user/verify/verify?tip=评论')
				return false
			}
			return true
		},
		openComment(parent_id) {
			if (this.checkComment()) {
				this.parent_comment_id = parent_id
				this.reply_user_id = 0
				this.comment_focus = true
				this.$refs.popupComment.open('bottom')
			}
		},
		clickReply(comment) {
			if (this.checkComment()) {
				this.parent_comment_id = comment.comment_id
				this.reply_user_id = comment.user_id
				this.comment_focus = true
				this.$refs.popupComment.open('bottom')
			}
		},
		clickAllComments() {
			Share.openLink('/pages/base/comments/comments?post_id=' + this.goods_id)
		},
		clickUser(user_id) {
			Share.openLink('/pages/user/home/<USER>' + user_id)
		},
		clickFollowUser(user_id) {
			Rest.post(Api.URL('user', 'follow_user'), {
				user_id: this.goods.author.user_id
			}).then(res => {
				this.$emit('zhuige_event_follow_user', {
					user_id: user_id,
					is_follow: res.data.is_follow
				})
			}, err => {
				console.log(err)
			})
		},
		sendComment() {
			if (!this.comment_content.trim()) {
				Tools.toast('请输入评论内容')
				return
			}
			
			Rest.post(Api.URL('comment', 'create'), {
				post_id: this.goods_id,
				content: this.comment_content,
				parent_comment_id: this.parent_comment_id,
				reply_user_id: this.reply_user_id
			}).then(res => {
				this.comment_content = ''
				this.$refs.popupComment.close()
				this.loadComments()
				Tools.toast('评论成功')
			}, err => {
				Tools.error(err.message)
			})
		},
		loadGoods() {
			Rest.post(Api.URL('idle', 'detail'), {
				goods_id: this.goods_id
			}).then(res => {
				uni.stopPullDownRefresh()
				if (res.code == 0) {
					this.goods = res.data
					this.loadComments()
				} else {
					Tools.toast(res.message)
				}
			}, err => {
				console.log(err)
			})
		},
		loadComments() {
			if (this.loadMore == 'loading') return
			
			this.loadMore = 'loading'
			Rest.post(Api.URL('comment', 'index'), {
				post_id: this.goods_id,
				offset: this.comments.length
			}).then(res => {
				this.comments = this.comments.concat(res.data.comments)
				this.loadMore = res.data.more
			}, err => {
				console.log(err)
				this.loadMore = 'more'
			})
		}
	}
}
</script>

<style>
.content {
	padding: 0 30rpx 180rpx;
}

.zhuige-goods-info {
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.zhuige-goods-price {
	align-items: baseline;
	color: #ff6146;
	display: flex;
	font-size: 40rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}

.zhuige-goods-price text:nth-child(1) {
	font-size: 28rpx;
	font-weight: 300;
	margin-right: 8rpx;
}

.zhuige-goods-title {
	color: #333;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 1.8em;
	margin-bottom: 20rpx;
}

.zhuige-goods-tags {
	display: flex;
	flex-wrap: wrap;
}

.zhuige-goods-tags text {
	background: #f5f5f5;
	border-radius: 6rpx;
	color: #333;
	font-size: 24rpx;
	margin-right: 12rpx;
	margin-bottom: 12rpx;
	padding: 8rpx 16rpx;
}

.zhuige-goods-tags text:first-child {
	background: #2c70db;
	color: #fff;
}

.zhuige-user-info {
	align-items: center;
	background: #fff;
	border-radius: 12rpx;
	display: flex;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.zhuige-user-avatar {
	margin-right: 20rpx;
	position: relative;
}

.zhuige-user-avatar image {
	border-radius: 50%;
	height: 80rpx;
	width: 80rpx;
}

.zhuige-user-certify {
	bottom: -8rpx;
	position: absolute;
	right: -8rpx;
}

.zhuige-user-certify image {
	height: 32rpx;
	width: 32rpx;
}

.zhuige-user-detail {
	flex: 1;
}

.zhuige-user-name {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.zhuige-user-sign {
	color: #999;
	font-size: 24rpx;
}

.zhuige-user-follow {
	background: #2c70db;
	border-radius: 20rpx;
	color: #fff;
	font-size: 24rpx;
	padding: 12rpx 24rpx;
}

.zhuige-goods-content {
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.zhuige-goods-content-title {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 20rpx;
}

.zhuige-rec-cat {
	align-items: center;
	background: #fff;
	border-radius: 12rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.zhuige-rec-cat > view:first-child {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
}

.zhuige-rec-cat > view:last-child {
	align-items: center;
	display: flex;
}

.zhuige-rec-cat text {
	color: #666;
	font-size: 28rpx;
	margin-right: 12rpx;
}

.zhuige-comment-box {
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.zhuige-comment-title {
	align-items: center;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.zhuige-comment-title text:first-child {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
}

.zhuige-comment-title text:last-child {
	color: #2c70db;
	font-size: 26rpx;
}

.zhuige-comment-item {
	border-bottom: 1rpx solid #f5f5f5;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
}

.zhuige-comment-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
	padding-bottom: 0;
}

.zhuige-comment-user {
	align-items: center;
	display: flex;
	margin-bottom: 12rpx;
}

.zhuige-comment-user image {
	border-radius: 50%;
	height: 48rpx;
	margin-right: 12rpx;
	width: 48rpx;
}

.zhuige-comment-user text {
	color: #666;
	font-size: 26rpx;
}

.zhuige-comment-content {
	color: #333;
	font-size: 28rpx;
	line-height: 1.6;
	margin-bottom: 12rpx;
}

.zhuige-comment-time {
	align-items: center;
	display: flex;
	justify-content: space-between;
}

.zhuige-comment-time text:first-child {
	color: #999;
	font-size: 24rpx;
}

.zhuige-comment-time text:last-child {
	color: #2c70db;
	font-size: 24rpx;
}

.zhuige-comment-more {
	color: #2c70db;
	font-size: 26rpx;
	text-align: center;
}

.zhuige-bottom-bar {
	align-items: center;
	background: #fff;
	bottom: 0;
	box-shadow: 0rpx 0rpx 6rpx rgba(99, 99, 99, 0.1);
	display: flex;
	height: 140rpx;
	justify-content: space-between;
	left: 0;
	padding: 0 30rpx 20rpx;
	position: fixed;
	width: 100%;
	z-index: 20;
}

.zhuige-bottom-left {
	align-items: center;
	display: flex;
}

.zhuige-bottom-left > view {
	align-items: center;
	display: flex;
	flex-direction: column;
	margin-right: 40rpx;
}

.zhuige-bottom-left text {
	color: #666;
	font-size: 22rpx;
	margin-top: 8rpx;
}

.zhuige-bottom-right {
	display: flex;
}

.zhuige-btn-sell {
	background: #f5f5f5;
	border-radius: 24rpx;
	color: #666;
	font-size: 28rpx;
	height: 80rpx;
	line-height: 80rpx;
	margin-right: 20rpx;
	padding: 0 32rpx;
	text-align: center;
}

.zhuige-btn-buy {
	background: #2c70db;
	border-radius: 24rpx;
	color: #fff;
	font-size: 28rpx;
	height: 80rpx;
	line-height: 80rpx;
	padding: 0 32rpx;
	text-align: center;
}

.zhuige-pop-qr {
	background: #fff;
	border-radius: 12rpx;
	padding: 40rpx;
	text-align: center;
}

.zhuige-pop-qr > view:first-child {
	margin-bottom: 20rpx;
}

.zhuige-pop-qr image {
	height: 400rpx;
	width: 400rpx;
}

.zhuige-pop-qr > view:last-child {
	color: #666;
	font-size: 28rpx;
}

.zhuige-comment-popup {
	background: #fff;
	padding: 30rpx;
}

.zhuige-comment-input {
	align-items: center;
	display: flex;
}

.zhuige-comment-input input {
	background: #f5f5f5;
	border-radius: 24rpx;
	flex: 1;
	font-size: 28rpx;
	height: 80rpx;
	line-height: 80rpx;
	margin-right: 20rpx;
	padding: 0 24rpx;
}

.zhuige-comment-send {
	background: #2c70db;
	border-radius: 24rpx;
	color: #fff;
	font-size: 28rpx;
	height: 80rpx;
	line-height: 80rpx;
	padding: 0 32rpx;
	text-align: center;
}
</style>
