<template>
	<view class="content">
		<!-- 商品标题 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-title-line">
					<input 
						v-model="title" 
						placeholder="请输入名称" 
						type="text"
					/>
				</view>
			</view>
		</view>
		
		<!-- 商品描述 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-input">
					<textarea 
						v-model="content" 
						maxlength="140" 
						placeholder="详细介绍有利于被推荐哦…"
					></textarea>
				</view>
			</view>
		</view>
		
		<!-- 图片上传 -->
		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view @click="clickImage" v-if="images.length < 9">
					<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
					<view>图片</view>
				</view>
				<view 
					class="loaded" 
					v-for="(image, index) in images" 
					:key="index"
				>
					<uni-icons 
						type="clear" 
						size="24" 
						color="#FD6531" 
						@click="clickDelImage(index)"
					></uni-icons>
					<image :src="image.image.url" mode="aspectFill" />
				</view>
			</view>
		</view>
		
		<!-- 分类选择 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-check">
					<view>选择分类：</view>
					<view>
						<picker 
							:range="cat_names" 
							:value="cur_cat" 
							@change="onCatChange"
						>
							<view>
								<view class="picker">{{ cats[cur_cat] ? cats[cur_cat].name : '' }}</view>
								<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 价格设置 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-price">
					<view>销售价格：</view>
					<view>
						<input 
							v-model="price" 
							placeholder="如:9.99" 
							type="number"
						/>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 发布协议 -->
		<view class="zhuige-post-box" v-if="fbxy">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-check">
					<view>
						<checkbox-group @change="onAgreeChange">
							<checkbox :checked="agree" />
						</checkbox-group>
					</view>
					<view @click="clickAgree">{{ fbxy }}</view>
				</view>
			</view>
		</view>
		
		<!-- 发布按钮 -->
		<view class="zhuige-post-box">
			<view class="zhuige-post-btn" @click="clickPost">
				{{ goods_id ? '保存修改' : '立即发布' }}
			</view>
		</view>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	data() {
		return {
			goods_id: 0,
			title: '',
			content: '',
			price: '',
			images: [],
			cats: [],
			cat_names: [],
			cur_cat: 0,
			fbxy: '',
			agree: false,
			post_require_mobile: 0,
			post_require_avatar: 0,
			post_require_weixin: 0
		}
	},
	onLoad(options) {
		if (options.goods_id) {
			this.goods_id = parseInt(options.goods_id)
		}
		if (options.cat_id) {
			this.default_cat_id = parseInt(options.cat_id)
		}
		this.loadSetting()
	},
	onShareAppMessage() {
		return {
			title: '发布闲置物品-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/post/post?n=n')
		}
	},
	onShareTimeline() {
		return {
			title: '发布闲置物品-' + getApp().globalData.appName
		}
	},
	methods: {
		clickImage() {
			uni.chooseImage({
				count: 9 - this.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadImages(res.tempFilePaths)
				}
			})
		},
		clickDelImage(index) {
			this.images.splice(index, 1)
		},
		onCatChange(e) {
			this.cur_cat = parseInt(e.detail.value)
		},
		onAgreeChange(e) {
			this.agree = e.detail.value.length > 0
		},
		clickAgree() {
			this.agree = !this.agree
		},
		uploadImages(tempFilePaths) {
			uni.showLoading({
				title: '上传中...'
			})
			
			let uploadPromises = tempFilePaths.map(filePath => {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: Api.URL('upload', 'image'),
						filePath: filePath,
						name: 'file',
						header: {
							'Authorization': 'Bearer ' + getApp().globalData.token
						},
						success: (res) => {
							try {
								const data = JSON.parse(res.data)
								if (data.code === 0) {
									resolve(data.data)
								} else {
									reject(data.message)
								}
							} catch (e) {
								reject('上传失败')
							}
						},
						fail: (err) => {
							reject('上传失败')
						}
					})
				})
			})
			
			Promise.all(uploadPromises).then(results => {
				uni.hideLoading()
				results.forEach(result => {
					this.images.push({
						image: {
							url: result.url
						}
					})
				})
			}).catch(error => {
				uni.hideLoading()
				Tools.toast(error)
			})
		},
		checkPermission() {
			if (this.post_require_mobile) {
				Share.openLink('/pages/user/login/login?type=mobile&tip=发布商品')
				return false
			}
			if (this.post_require_avatar) {
				Share.openLink('/pages/user/verify/verify?tip=发布商品')
				return false
			}
			if (this.post_require_weixin) {
				Share.openLink('/pages/user/weixin/weixin?tip=发布商品')
				return false
			}
			return true
		},
		clickPost() {
			if (!this.checkPermission()) {
				return
			}
			
			if (!this.title.trim()) {
				Tools.toast('请输入商品名称')
				return
			}
			if (!this.content.trim()) {
				Tools.toast('请输入商品描述')
				return
			}
			if (this.images.length === 0) {
				Tools.toast('请上传商品图片')
				return
			}
			if (!this.cats[this.cur_cat]) {
				Tools.toast('请选择商品分类')
				return
			}
			if (!this.price || parseFloat(this.price) < 0) {
				Tools.toast('请输入正确的价格')
				return
			}
			if (this.fbxy && !this.agree) {
				Tools.toast('请同意发布协议')
				return
			}
			
			uni.showLoading({
				title: this.goods_id ? '保存中...' : '发布中...'
			})
			
			const imageUrls = this.images.map(img => img.image.url)
			
			Rest.post(Api.URL('idle', 'post_goods'), {
				goods_id: this.goods_id,
				title: this.title,
				content: this.content,
				price: this.price,
				cat_id: this.cats[this.cur_cat].id,
				images: imageUrls
			}).then(res => {
				uni.hideLoading()
				Tools.toast(res.data.message)
				setTimeout(() => {
					if (this.goods_id) {
						Share.navigateBack()
					} else {
						Share.openLink('/pages/idle-shop/detail/detail?id=' + res.data.goods_id)
					}
				}, 1500)
			}, err => {
				uni.hideLoading()
				if (err.code === 'require_mobile') {
					Share.openLink('/pages/user/login/login?type=mobile&tip=发布商品')
				} else if (err.code === 'require_avatar') {
					Share.openLink('/pages/user/verify/verify?tip=发布商品')
				} else if (err.code === 'require_weixin') {
					Share.openLink('/pages/user/weixin/weixin?tip=发布商品')
				} else {
					Tools.error(err.message)
				}
			})
		},
		loadSetting() {
			Rest.post(Api.URL('idle', 'post_setting'), {}).then(res => {
				this.cats = res.data.cats
				this.cat_names = res.data.cats.map(cat => cat.name)
				this.fbxy = res.data.fbxy
				this.post_require_mobile = res.data.post_require_mobile
				this.post_require_avatar = res.data.post_require_avatar
				this.post_require_weixin = res.data.post_require_weixin
				
				// 设置默认分类
				if (this.default_cat_id) {
					for (let i = 0; i < this.cats.length; i++) {
						if (this.cats[i].id === this.default_cat_id) {
							this.cur_cat = i
							break
						}
					}
				}
				
				// 如果是编辑模式，加载商品信息
				if (this.goods_id) {
					this.loadGoodsInfo()
				}
			}, err => {
				console.log(err)
			})
		},
		loadGoodsInfo() {
			Rest.post(Api.URL('idle', 'edit_goods'), {
				goods_id: this.goods_id
			}).then(res => {
				this.title = res.data.title
				this.content = res.data.content
				this.price = res.data.price
				
				// 设置分类
				for (let i = 0; i < this.cats.length; i++) {
					if (this.cats[i].id === res.data.cat_id) {
						this.cur_cat = i
						break
					}
				}
				
				// 设置图片
				this.images = res.data.images.map(url => ({
					image: { url: url }
				}))
				
				uni.setNavigationBarTitle({
					title: '编辑商品'
				})
			}, err => {
				Tools.error(err.message)
			})
		}
	}
}
</script>

<style>
.content {
	height: 100%;
	overflow-y: scroll;
	position: fixed;
	width: 100%;
}

.zhuige-post-box {
	padding: 0 20rpx;
}

.content .zhuige-post-box:nth-last-child(2) {
	margin-bottom: 180rpx;
}

.zhuige-post-line {
	align-items: center;
	display: flex;
	height: 2.2em;
	line-height: 2.2em;
}

.zhuige-post-line > view:nth-child(1) {
	font-size: 30rpx;
	font-weight: 400;
	width: 156rpx;
}

.zhuige-post-line > view:nth-child(2) {
	align-items: center;
	display: flex;
	max-width: 510rpx;
}

.zhuige-post-line view:nth-child(2) view {
	font-size: 28rpx;
	font-weight: 400;
	height: 1.6rem;
	line-height: 1.6rem;
	margin-right: 12rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.zhuige-post-line view:nth-child(2) input {
	text-align: left;
	width: 450rpx;
}

.zhuige-upload-set {
	align-items: center;
	display: flex;
	flex-wrap: wrap;
}

.zhuige-upload-set > view {
	background: #fff;
	border-radius: 12rpx;
	height: 154rpx;
	margin: 0 20rpx 20rpx 0;
	position: relative;
	text-align: center;
	width: 154rpx;
}

.zhuige-upload-set > view view {
	color: #777;
	font-size: 28rpx;
	font-weight: 400;
	height: 1em;
	line-height: 1em;
	margin-top: -28rpx;
}

.zhuige-upload-set view.loaded image {
	border-radius: 12rpx;
	height: 100%;
	width: 100%;
}

.zhuige-upload-set > view.loaded uni-icons {
	position: absolute;
	right: -20rpx;
	top: -46rpx;
	z-index: 3;
}

.zhuige-post-input textarea {
	font-size: 30rpx;
	height: 240rpx;
	line-height: normal;
	padding: 20rpx 0;
}

.zhuige-post-title-line {
	width: 100%;
}

.zhuige-post-title-line input {
	font-size: 30rpx;
	width: 100%;
}

.zhuige-post-btn {
	background: #2c70db;
	border-radius: 12rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	height: 88rpx;
	line-height: 88rpx;
	margin: 40rpx 0;
	text-align: center;
}

.line-check {
	align-items: center;
}

.line-check checkbox-group {
	margin-right: 20rpx;
}

.line-price input {
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	font-size: 28rpx;
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 20rpx;
	width: 200rpx;
}

.picker {
	color: #333;
	font-size: 28rpx;
	min-width: 120rpx;
}
</style>
