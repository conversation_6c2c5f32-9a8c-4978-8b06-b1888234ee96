<template>
	<view class="content">
		<block v-if="list.length > 0">
			<view 
				class="zhuige-idel-block" 
				v-for="(goods, index) in list" 
				:key="index"
				@click="clickGoods(goods)"
			>
				<view class="zhuige-idle-cover">
					<text v-if="goods.stick">推广</text>
					<image :src="goods.thumb" mode="aspectFill" />
				</view>
				<view class="zhuige-idle-info">
					<view class="zhuige-idle-info-title">{{ goods.title }}</view>
					<view class="zhuige-idle-info-price">
						<text>￥</text>
						<text>{{ goods.price }}</text>
					</view>
					<view class="zhuige-idle-info-act">
						<view>
							<image :src="goods.user.avatar" mode="aspectFill" />
							<text>{{ goods.user.nickname }}</text>
						</view>
						<view>
							<text @click.stop="clickSell(goods.cat.id)">卖同款</text>
							<text @click.stop="clickBuy(goods.user.weixin)">我想要</text>
						</view>
					</view>
				</view>
			</view>
			<uni-load-more :status="loadMore"></uni-load-more>
		</block>
		<block v-else>
			<zhuige-nodata v-if="loaded"></zhuige-nodata>
		</block>
		
		<!-- 二维码弹窗 -->
		<uni-popup ref="popupQrcode" type="center">
			<view class="zhuige-pop-qr">
				<view>
					<image :src="qrcode" mode="aspectFill" :show-menu-by-longpress="true" />
				</view>
				<view>扫一扫/长按加好友</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	components: {
		ZhuigeNodata: () => import('@/components/zhuige-nodata')
	},
	data() {
		return {
			title: '',
			cat_id: 0,
			search: '',
			list: [],
			loadMore: 'more',
			loaded: false,
			qrcode: ''
		}
	},
	onLoad(options) {
		if (options.title) {
			this.title = options.title
			uni.setNavigationBarTitle({
				title: this.title
			})
		}
		if (options.cat_id) {
			this.cat_id = parseInt(options.cat_id)
		}
		if (options.search) {
			this.search = options.search
		}
		this.loadGoods(true)
	},
	onReachBottom() {
		if (this.loadMore == 'more') {
			this.loadGoods(false)
		}
	},
	onPullDownRefresh() {
		this.loadGoods(true)
	},
	onShareAppMessage() {
		return {
			title: this.title + '-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/list/list?title=' + this.title + '&cat_id=' + this.cat_id + '&search=' + this.search + '&n=n')
		}
	},
	onShareTimeline() {
		return {
			title: this.title + '-' + getApp().globalData.appName
		}
	},
	methods: {
		clickGoods(goods) {
			Share.openLink('/pages/idle-shop/detail/detail?id=' + goods.id)
		},
		clickSell(cat_id) {
			let url = '/pages/idle-shop/post/post'
			if (cat_id) {
				url += '?cat_id=' + cat_id
			}
			Share.openLink(url)
		},
		clickBuy(weixin) {
			if (weixin) {
				this.qrcode = weixin
				this.$refs.popupQrcode.open('center')
			} else {
				Tools.toast('该用户未设置微信二维码')
			}
		},
		loadGoods(refresh) {
			let url = Api.URL('idle', 'list')
			Rest.post(url, {
				cat_id: this.cat_id,
				search: this.search,
				offset: refresh ? 0 : this.list.length
			}).then(res => {
				this.list = refresh ? res.data.list : this.list.concat(res.data.list)
				this.loadMore = res.data.more
				this.loaded = true
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			}, err => {
				console.log(err)
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			})
		}
	}
}
</script>

<style>
.zhuige-idel-block {
	background: #fff;
	border-radius: 12rpx;
	display: flex;
	margin: 20rpx;
	overflow: hidden;
	padding: 20rpx;
}

.zhuige-idle-cover {
	margin-right: 20rpx;
	position: relative;
	width: 200rpx;
}

.zhuige-idle-cover text {
	background: #ff6146;
	border-radius: 6rpx 0 6rpx 0;
	color: #fff;
	font-size: 22rpx;
	height: 48rpx;
	left: 0;
	line-height: 48rpx;
	padding: 0 24rpx;
	position: absolute;
	top: 0;
	z-index: 3;
}

.zhuige-idle-cover image {
	border-radius: 12rpx;
	height: 200rpx;
	width: 200rpx;
}

.zhuige-idle-info {
	display: flex;
	flex: 1;
	flex-direction: column;
	justify-content: space-between;
}

.zhuige-idle-info-title {
	color: #333;
	font-size: 32rpx;
	font-weight: 600;
	line-height: 1.4;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.zhuige-idle-info-price {
	align-items: center;
	display: flex;
	margin: 20rpx 0;
}

.zhuige-idle-info-price text:first-child {
	color: #333;
	font-size: 24rpx;
	font-weight: 300;
}

.zhuige-idle-info-price text:last-child {
	color: #333;
	font-size: 36rpx;
	font-weight: 600;
	margin-left: 8rpx;
}

.zhuige-idle-info-act {
	align-items: center;
	display: flex;
	justify-content: space-between;
}

.zhuige-idle-info-act > view:first-child {
	align-items: center;
	display: flex;
}

.zhuige-idle-info-act > view:first-child image {
	border-radius: 50%;
	height: 48rpx;
	margin-right: 12rpx;
	width: 48rpx;
}

.zhuige-idle-info-act > view:first-child text {
	color: #666;
	font-size: 24rpx;
}

.zhuige-idle-info-act > view:last-child {
	display: flex;
}

.zhuige-idle-info-act > view:last-child text {
	background: #f5f5f5;
	border-radius: 20rpx;
	color: #666;
	font-size: 24rpx;
	margin-left: 12rpx;
	padding: 8rpx 16rpx;
}

.zhuige-idle-info-act > view:last-child text:first-child {
	background: #2c70db;
	color: #fff;
}

.zhuige-pop-qr {
	background: #fff;
	border-radius: 12rpx;
	padding: 40rpx;
	text-align: center;
}

.zhuige-pop-qr > view:first-child {
	margin-bottom: 20rpx;
}

.zhuige-pop-qr image {
	height: 400rpx;
	width: 400rpx;
}

.zhuige-pop-qr > view:last-child {
	color: #666;
	font-size: 28rpx;
}
</style>
