<?php

if (!defined('ABSPATH')) {
    exit;
}

$prefix = 'zhuige_idle_options';

//
// 创建选项
//
CSF::createOptions($prefix, array(
    'menu_title' => '闲置物品',
    'menu_slug'  => 'zhuige-idle',
    'menu_icon'  => 'dashicons-store',
    'framework_title' => '闲置物品设置',
    'menu_position' => 30,
));

//
// 首页设置
//
CSF::createSection($prefix, array(
    'title' => '首页设置',
    'icon'  => 'fas fa-home',
    'fields' => array(

        array(
            'id'      => 'idle_background',
            'type'    => 'media',
            'title'   => '背景图',
            'subtitle' => '首页头部背景图',
            'library' => 'image',
        ),

        array(
            'id'      => 'idle_slides',
            'type'    => 'group',
            'title'   => '轮播图',
            'fields'  => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'      => 'idle_icons',
            'type'    => 'group',
            'title'   => '热门图标',
            'fields'  => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'      => 'idle_nav_cats',
            'type'    => 'select',
            'title'   => '导航分类',
            'subtitle' => '选择在首页导航显示的分类',
            'options' => 'categories',
            'query_args' => array(
                'taxonomy' => 'idle_category',
            ),
            'multiple' => true,
        ),

        array(
            'id'      => 'idle_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '分享时显示的图片',
            'library' => 'image',
        ),

    )
));

//
// 列表页广告
//
CSF::createSection($prefix, array(
    'title' => '列表页广告',
    'icon'  => 'fas fa-ad',
    'fields' => array(

        array(
            'id'      => 'idle_list_ad',
            'type'    => 'group',
            'title'   => '广告配置',
            'fields'  => array(
                array(
                    'id'    => 'first',
                    'type'  => 'number',
                    'title' => '首次出现位置',
                    'default' => 3,
                ),
                array(
                    'id'    => 'frequency',
                    'type'  => 'number',
                    'title' => '出现频率',
                    'subtitle' => '每隔多少个商品出现一次',
                    'default' => 10,
                ),
                array(
                    'id'      => 'items',
                    'type'    => 'group',
                    'title'   => '广告内容',
                    'fields'  => array(
                        array(
                            'id'      => 'image',
                            'type'    => 'media',
                            'title'   => '图片',
                            'library' => 'image',
                        ),
                        array(
                            'id'    => 'link',
                            'type'  => 'text',
                            'title' => '链接',
                        ),
                    ),
                ),
            ),
        ),

    )
));

//
// 底部菜单
//
CSF::createSection($prefix, array(
    'title' => '底部菜单',
    'icon'  => 'fas fa-bars',
    'fields' => array(

        array(
            'id'      => 'idle_bottom_menu',
            'type'    => 'group',
            'title'   => '菜单项',
            'fields'  => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

    )
));

//
// 发布设置
//
CSF::createSection($prefix, array(
    'title' => '发布设置',
    'icon'  => 'fas fa-plus',
    'fields' => array(

        array(
            'id'    => 'idle_post_require_mobile',
            'type'  => 'switcher',
            'title' => '发布需要手机号',
            'subtitle' => '用户发布商品时是否需要绑定手机号',
            'default' => true,
        ),

        array(
            'id'    => 'idle_post_require_avatar',
            'type'  => 'switcher',
            'title' => '发布需要头像',
            'subtitle' => '用户发布商品时是否需要设置头像昵称',
            'default' => true,
        ),

        array(
            'id'    => 'idle_post_require_weixin',
            'type'  => 'switcher',
            'title' => '发布需要微信二维码',
            'subtitle' => '用户发布商品时是否需要设置微信二维码',
            'default' => true,
        ),

        array(
            'id'      => 'idle_fbxy',
            'type'    => 'textarea',
            'title'   => '发布协议',
            'subtitle' => '用户发布商品时需要同意的协议内容',
            'default' => '我同意遵守平台发布规则，确保商品信息真实有效。',
        ),

    )
));

//
// 评论设置
//
CSF::createSection($prefix, array(
    'title' => '评论设置',
    'icon'  => 'fas fa-comments',
    'fields' => array(

        array(
            'id'    => 'idle_comment_switch',
            'type'  => 'switcher',
            'title' => '评论功能',
            'subtitle' => '是否开启商品评论功能',
            'default' => true,
        ),

        array(
            'id'    => 'idle_comment_require_mobile',
            'type'  => 'switcher',
            'title' => '评论需要手机号',
            'subtitle' => '用户评论时是否需要绑定手机号',
            'default' => false,
            'dependency' => array('idle_comment_switch', '==', true),
        ),

        array(
            'id'    => 'idle_comment_require_avatar',
            'type'  => 'switcher',
            'title' => '评论需要头像',
            'subtitle' => '用户评论时是否需要设置头像昵称',
            'default' => false,
            'dependency' => array('idle_comment_switch', '==', true),
        ),

    )
));
