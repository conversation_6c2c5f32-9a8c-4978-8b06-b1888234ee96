<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 闲置物品后台管理
 */
class ZhuiGe_Xcx_Idle_Admin
{
    public function __construct()
    {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
    }

    /**
     * 添加管理菜单
     */
    public function add_admin_menu()
    {
        add_submenu_page(
            'edit.php?post_type=zhuige_idle_goods',
            '闲置设置',
            '闲置设置',
            'manage_options',
            'zhuige-idle-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * 初始化管理页面
     */
    public function admin_init()
    {
        register_setting('zhuige_idle_settings', 'zhuige_idle_options');

        add_settings_section(
            'zhuige_idle_general',
            '基本设置',
            array($this, 'general_section_callback'),
            'zhuige_idle_settings'
        );

        add_settings_field(
            'enable_module',
            '启用模块',
            array($this, 'enable_module_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'page_title',
            '页面标题',
            array($this, 'page_title_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'page_desc',
            '页面描述',
            array($this, 'page_desc_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'allow_guest_view',
            '允许游客浏览',
            array($this, 'allow_guest_view_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'allow_guest_post',
            '允许游客发布',
            array($this, 'allow_guest_post_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'auto_approve',
            '自动审核通过',
            array($this, 'auto_approve_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'max_images',
            '最大图片数量',
            array($this, 'max_images_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );

        add_settings_field(
            'post_agreement',
            '发布协议链接',
            array($this, 'post_agreement_callback'),
            'zhuige_idle_settings',
            'zhuige_idle_general'
        );
    }

    /**
     * 设置页面
     */
    public function settings_page()
    {
        ?>
        <div class="wrap">
            <h1>闲置物品设置</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('zhuige_idle_settings');
                do_settings_sections('zhuige_idle_settings');
                submit_button();
                ?>
            </form>
            
            <div class="card" style="margin-top: 20px;">
                <h2>使用说明</h2>
                <p>闲置物品模块允许用户发布和交易二手物品，支持以下功能：</p>
                <ul>
                    <li>商品发布和编辑</li>
                    <li>分类管理</li>
                    <li>价格设置</li>
                    <li>图片上传</li>
                    <li>用户交流</li>
                    <li>收藏和点赞</li>
                </ul>
                
                <h3>小程序页面路径</h3>
                <ul>
                    <li>首页：/pages/idle-shop/index/index</li>
                    <li>详情页：/pages/idle-shop/detail/detail</li>
                    <li>发布页：/pages/idle-shop/post/post</li>
                    <li>列表页：/pages/idle-shop/list/list</li>
                    <li>分类页：/pages/idle-shop/classify/classify</li>
                </ul>
            </div>
        </div>
        <?php
    }

    /**
     * 基本设置区块回调
     */
    public function general_section_callback()
    {
        echo '<p>配置闲置物品模块的基本设置。</p>';
    }

    /**
     * 启用模块回调
     */
    public function enable_module_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['enable_module']) ? $options['enable_module'] : 1;
        echo '<input type="checkbox" name="zhuige_idle_options[enable_module]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>启用闲置物品模块</label>';
    }

    /**
     * 页面标题回调
     */
    public function page_title_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['page_title']) ? $options['page_title'] : '闲置物品';
        echo '<input type="text" name="zhuige_idle_options[page_title]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">设置闲置物品页面的标题</p>';
    }

    /**
     * 页面描述回调
     */
    public function page_desc_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['page_desc']) ? $options['page_desc'] : '发现身边的闲置好物';
        echo '<textarea name="zhuige_idle_options[page_desc]" rows="3" class="large-text">' . esc_textarea($value) . '</textarea>';
        echo '<p class="description">设置闲置物品页面的描述</p>';
    }

    /**
     * 允许游客浏览回调
     */
    public function allow_guest_view_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['allow_guest_view']) ? $options['allow_guest_view'] : 1;
        echo '<input type="checkbox" name="zhuige_idle_options[allow_guest_view]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>允许未登录用户浏览闲置物品</label>';
    }

    /**
     * 允许游客发布回调
     */
    public function allow_guest_post_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['allow_guest_post']) ? $options['allow_guest_post'] : 0;
        echo '<input type="checkbox" name="zhuige_idle_options[allow_guest_post]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>允许未登录用户发布闲置物品</label>';
    }

    /**
     * 自动审核通过回调
     */
    public function auto_approve_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['auto_approve']) ? $options['auto_approve'] : 1;
        echo '<input type="checkbox" name="zhuige_idle_options[auto_approve]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>新发布的闲置物品自动审核通过</label>';
    }

    /**
     * 最大图片数量回调
     */
    public function max_images_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['max_images']) ? $options['max_images'] : 9;
        echo '<input type="number" name="zhuige_idle_options[max_images]" value="' . esc_attr($value) . '" min="1" max="20" />';
        echo '<p class="description">设置每个商品最多可以上传的图片数量</p>';
    }

    /**
     * 发布协议链接回调
     */
    public function post_agreement_callback()
    {
        $options = get_option('zhuige_idle_options');
        $value = isset($options['post_agreement']) ? $options['post_agreement'] : '';
        echo '<input type="url" name="zhuige_idle_options[post_agreement]" value="' . esc_attr($value) . '" class="large-text" />';
        echo '<p class="description">设置发布协议页面的链接，留空则不显示协议</p>';
    }
}

// 初始化管理类
new ZhuiGe_Xcx_Idle_Admin();

/**
 * 添加自定义列到商品列表
 */
function zhuige_idle_add_custom_columns($columns)
{
    $new_columns = array();
    
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        
        if ($key === 'title') {
            $new_columns['idle_price'] = '价格';
            $new_columns['idle_category'] = '分类';
            $new_columns['idle_condition'] = '成色';
        }
    }
    
    return $new_columns;
}
add_filter('manage_zhuige_idle_goods_posts_columns', 'zhuige_idle_add_custom_columns');

/**
 * 显示自定义列内容
 */
function zhuige_idle_custom_column_content($column, $post_id)
{
    switch ($column) {
        case 'idle_price':
            $options = get_post_meta($post_id, 'zhuige-idle-goods-option', true);
            $price = isset($options['price']) ? $options['price'] : '0';
            echo '￥' . $price;
            break;
            
        case 'idle_category':
            $terms = get_the_terms($post_id, 'idle_category');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                }
                echo implode(', ', $term_names);
            } else {
                echo '未分类';
            }
            break;
            
        case 'idle_condition':
            $options = get_post_meta($post_id, 'zhuige-idle-goods-option', true);
            $condition = isset($options['condition_type']) ? $options['condition_type'] : 'good';
            $condition_labels = array(
                'new' => '全新',
                'good' => '九成新',
                'fair' => '八成新',
                'poor' => '七成新以下'
            );
            echo isset($condition_labels[$condition]) ? $condition_labels[$condition] : '九成新';
            break;
    }
}
add_action('manage_zhuige_idle_goods_posts_custom_column', 'zhuige_idle_custom_column_content', 10, 2);

/**
 * 使自定义列可排序
 */
function zhuige_idle_sortable_columns($columns)
{
    $columns['idle_price'] = 'idle_price';
    return $columns;
}
add_filter('manage_edit-zhuige_idle_goods_sortable_columns', 'zhuige_idle_sortable_columns');
