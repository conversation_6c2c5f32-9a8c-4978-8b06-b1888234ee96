<?php

/**
 * 追格小程序 - 闲置物品控制器
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Idle_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->module = 'idle';
        $this->routes = [
            'setting' => 'get_setting',
            'list' => 'get_goods_list',
            'detail' => 'get_goods_detail',
            'cats' => 'get_categories',
            'create' => 'create_goods',
            'edit' => 'edit_goods',
            'edit_goods' => 'get_edit_goods',
        ];
    }

    /**
     * 获取设置信息
     */
    public function get_setting($request)
    {
        $data = [];

        // 轮播图
        $slides = ZhuiGe_Xcx::option_value('idle_slides');
        $data['slides'] = [];
        if ($slides && is_array($slides)) {
            foreach ($slides as $slide) {
                if (!empty($slide['image'])) {
                    $data['slides'][] = [
                        'image' => ZhuiGe_Xcx::option_image_url($slide['image']),
                        'link' => $slide['link'] ?? '',
                        'title' => $slide['title'] ?? ''
                    ];
                }
            }
        }

        // 图标导航
        $icons = ZhuiGe_Xcx::option_value('idle_icons');
        $data['icons'] = [];
        if ($icons && is_array($icons)) {
            foreach ($icons as $icon) {
                if (!empty($icon['image'])) {
                    $data['icons'][] = [
                        'image' => ZhuiGe_Xcx::option_image_url($icon['image']),
                        'link' => $icon['link'] ?? '',
                        'title' => $icon['title'] ?? ''
                    ];
                }
            }
        }

        // 分类导航
        $data['nav_cats'] = zhuige_idle_get_categories();

        // 列表广告
        $data['list_ad'] = [];

        // 底部菜单
        $bottom_menu = ZhuiGe_Xcx::option_value('idle_bottom_menu');
        $data['bottom_menu'] = [];
        if ($bottom_menu && is_array($bottom_menu)) {
            foreach ($bottom_menu as $menu) {
                if (!empty($menu['image'])) {
                    $data['bottom_menu'][] = [
                        'image' => ZhuiGe_Xcx::option_image_url($menu['image']),
                        'link' => $menu['link'] ?? '',
                        'title' => $menu['title'] ?? ''
                    ];
                }
            }
        }

        // 背景图
        $data['background'] = ZhuiGe_Xcx::option_image_url(ZhuiGe_Xcx::option_value('idle_background'));

        // 分享图
        $data['share_img'] = ZhuiGe_Xcx::option_image_url(ZhuiGe_Xcx::option_value('idle_share_img'));

        return $this->success($data);
    }

    /**
     * 获取商品列表
     */
    public function get_goods_list($request)
    {
        $cat_id = $request->get_param('cat_id');
        $search = $request->get_param('search');
        $offset = $request->get_param('offset') ?: 0;
        $limit = 20;

        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
        ];

        if ($cat_id) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ],
            ];
        }

        if ($search) {
            $args['s'] = $search;
        }

        $query = new WP_Query($args);
        $list = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $list[] = zhuige_idle_shop_goods_format(get_post());
            }
            wp_reset_postdata();
        }

        $data = [
            'list' => $list,
            'more' => (count($list) >= $limit ? 'more' : 'nomore')
        ];

        return $this->success($data);
    }

    /**
     * 获取商品详情
     */
    public function get_goods_detail($request)
    {
        $goods_id = $request->get_param('goods_id');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods' || $post->post_status !== 'publish') {
            return $this->error('商品不存在');
        }

        // 增加浏览量
        $views = (int) get_post_meta($goods_id, 'zhuige_views', true);
        update_post_meta($goods_id, 'zhuige_views', $views + 1);

        $goods = zhuige_idle_goods_detail_format($post);

        return $this->success($goods);
    }

    /**
     * 获取分类列表
     */
    public function get_categories($request)
    {
        $cats = zhuige_idle_get_categories(true);
        
        $data = [
            'cats' => $cats,
            'fbxy' => ZhuiGe_Xcx::option_value('idle_post_agreement') // 发布协议链接
        ];

        return $this->success($data);
    }

    /**
     * 创建商品
     */
    public function create_goods($request)
    {
        if (!is_user_logged_in()) {
            return $this->error('请先登录');
        }
        
        $title = $request->get_param('title');
        $content = $request->get_param('content');
        $images = $request->get_param('images');
        $cat_id = $request->get_param('cat_id');
        $price = $request->get_param('price');

        if (!$title) {
            return $this->error('商品名称不能为空');
        }

        if (!$content) {
            return $this->error('商品描述不能为空');
        }

        if (!$cat_id) {
            return $this->error('请选择商品分类');
        }

        if (!$price || floatval($price) <= 0) {
            return $this->error('请输入正确的价格');
        }

        $current_user_id = get_current_user_id();
        
        // 创建文章
        $post_data = [
            'post_title' => sanitize_text_field($title),
            'post_content' => wp_kses_post($content),
            'post_status' => 'publish',
            'post_type' => 'zhuige_idle_goods',
            'post_author' => $current_user_id
        ];

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            return $this->error('发布失败，请重试');
        }

        // 设置分类
        wp_set_post_terms($post_id, [$cat_id], 'idle_category');

        // 保存商品信息
        $options = [
            'price' => sanitize_text_field($price),
            'original_price' => '',
            'condition_type' => 'good',
            'location' => '',
            'contact_info' => '',
            'trade_type' => 'both',
            'is_negotiable' => 1
        ];
        update_post_meta($post_id, 'zhuige_idle_goods_options', $options);

        // 保存图片
        if ($images) {
            $image_list = json_decode($images, true);
            if (is_array($image_list) && !empty($image_list)) {
                // 设置第一张图片为特色图片
                $first_image = $image_list[0];
                if (isset($first_image) && is_string($first_image)) {
                    $attachment_id = $this->get_attachment_id_by_url($first_image);
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                    }
                }
                
                // 保存所有图片
                update_post_meta($post_id, 'zhuige_idle_images', $images);
            }
        }

        $data = [
            'goods_id' => $post_id
        ];

        return $this->success($data, '发布成功！');
    }

    /**
     * 编辑商品
     */
    public function edit_goods($request)
    {
        if (!is_user_logged_in()) {
            return $this->error('请先登录');
        }
        
        $goods_id = $request->get_param('goods_id');
        $title = $request->get_param('title');
        $content = $request->get_param('content');
        $images = $request->get_param('images');
        $cat_id = $request->get_param('cat_id');
        $price = $request->get_param('price');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods') {
            return $this->error('商品不存在');
        }

        $current_user_id = get_current_user_id();
        if ($post->post_author != $current_user_id && !current_user_can('edit_others_posts')) {
            return $this->error('没有权限编辑此商品');
        }

        if (!$title) {
            return $this->error('商品名称不能为空');
        }

        if (!$content) {
            return $this->error('商品描述不能为空');
        }

        if (!$cat_id) {
            return $this->error('请选择商品分类');
        }

        if (!$price || floatval($price) <= 0) {
            return $this->error('请输入正确的价格');
        }

        // 更新文章
        $post_data = [
            'ID' => $goods_id,
            'post_title' => sanitize_text_field($title),
            'post_content' => wp_kses_post($content)
        ];

        $result = wp_update_post($post_data);

        if (is_wp_error($result)) {
            return $this->error('更新失败，请重试');
        }

        // 更新分类
        wp_set_post_terms($goods_id, [$cat_id], 'idle_category');

        // 更新商品信息
        $options = get_post_meta($goods_id, 'zhuige_idle_goods_options', true);
        if (!$options) {
            $options = [];
        }
        $options['price'] = sanitize_text_field($price);
        update_post_meta($goods_id, 'zhuige_idle_goods_options', $options);

        // 更新图片
        if ($images) {
            $image_list = json_decode($images, true);
            if (is_array($image_list) && !empty($image_list)) {
                // 设置第一张图片为特色图片
                $first_image = $image_list[0];
                if (isset($first_image) && is_string($first_image)) {
                    $attachment_id = $this->get_attachment_id_by_url($first_image);
                    if ($attachment_id) {
                        set_post_thumbnail($goods_id, $attachment_id);
                    }
                }
                
                // 保存所有图片
                update_post_meta($goods_id, 'zhuige_idle_images', $images);
            }
        }

        return $this->success(null, '更新成功！');
    }

    /**
     * 获取编辑商品信息
     */
    public function get_edit_goods($request)
    {
        if (!is_user_logged_in()) {
            return $this->error('请先登录');
        }
        
        $goods_id = $request->get_param('goods_id');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods') {
            return $this->error('商品不存在');
        }

        $current_user_id = get_current_user_id();
        if ($post->post_author != $current_user_id && !current_user_can('edit_others_posts')) {
            return $this->error('没有权限编辑此商品');
        }

        $options = get_post_meta($goods_id, 'zhuige_idle_goods_options', true);
        $images_json = get_post_meta($goods_id, 'zhuige_idle_images', true);
        $images = [];
        
        if ($images_json) {
            $image_list = json_decode($images_json, true);
            if (is_array($image_list)) {
                $images = $image_list;
            }
        }

        // 获取分类
        $categories = wp_get_post_terms($goods_id, 'idle_category');
        $cat_id = 0;
        if (!empty($categories)) {
            $cat_id = $categories[0]->term_id;
        }

        $data = [
            'title' => $post->post_title,
            'content' => $post->post_content,
            'price' => $options['price'] ?? '0',
            'cat_id' => $cat_id,
            'images' => $images
        ];

        return $this->success($data);
    }

    /**
     * 根据URL获取附件ID
     */
    private function get_attachment_id_by_url($url)
    {
        global $wpdb;
        
        $attachment = $wpdb->get_col($wpdb->prepare("SELECT ID FROM {$wpdb->posts} WHERE guid='%s';", $url));
        
        if (!empty($attachment)) {
            return $attachment[0];
        }
        
        return false;
    }
}
