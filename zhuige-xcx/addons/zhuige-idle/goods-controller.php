<?php

if (!defined('ABSPATH')) {
    exit;
}

class ZhuiGe_Xcx_Idle_Goods_Controller extends ZhuiGe_Xcx_Base_Controller
{
    /**
     * 首页设置
     */
    public function setting()
    {
        $data = array();

        // 背景图
        $background = ZhuiGe_Xcx::option_value('idle_background');
        if ($background) {
            $data['background'] = ZhuiGe_Xcx::option_image_url($background);
        }

        // 轮播图
        $slides = ZhuiGe_Xcx::option_value('idle_slides');
        $data['slides'] = array();
        if ($slides && is_array($slides)) {
            foreach ($slides as $slide) {
                if (!empty($slide['image'])) {
                    $data['slides'][] = array(
                        'image' => ZhuiGe_Xcx::option_image_url($slide['image']),
                        'link' => $slide['link'] ?? ''
                    );
                }
            }
        }

        // 热门图标
        $icons = ZhuiGe_Xcx::option_value('idle_icons');
        $data['icons'] = array();
        if ($icons && is_array($icons)) {
            foreach ($icons as $icon) {
                if (!empty($icon['image']) && !empty($icon['title'])) {
                    $data['icons'][] = array(
                        'image' => ZhuiGe_Xcx::option_image_url($icon['image']),
                        'title' => $icon['title'],
                        'link' => $icon['link'] ?? ''
                    );
                }
            }
        }

        // 导航分类
        $nav_cats_ids = ZhuiGe_Xcx::option_value('idle_nav_cats');
        $data['nav_cats'] = array();
        if ($nav_cats_ids && is_array($nav_cats_ids)) {
            // 添加"最新"选项
            $data['nav_cats'][] = array(
                'id' => 0,
                'title' => '最新'
            );
            
            foreach ($nav_cats_ids as $cat_id) {
                $category = get_term($cat_id, 'idle_category');
                if ($category && !is_wp_error($category)) {
                    $data['nav_cats'][] = array(
                        'id' => $category->term_id,
                        'title' => $category->name
                    );
                }
            }
        } else {
            // 如果没有设置，默认显示"最新"
            $data['nav_cats'][] = array(
                'id' => 0,
                'title' => '最新'
            );
        }

        // 列表页广告
        $list_ad = ZhuiGe_Xcx::option_value('idle_list_ad');
        $data['list_ad'] = array();
        if ($list_ad && is_array($list_ad)) {
            foreach ($list_ad as $ad) {
                if (!empty($ad['items']) && is_array($ad['items'])) {
                    $ad_items = array();
                    foreach ($ad['items'] as $item) {
                        if (!empty($item['image'])) {
                            $ad_items[] = array(
                                'image' => ZhuiGe_Xcx::option_image_url($item['image']),
                                'link' => $item['link'] ?? ''
                            );
                        }
                    }
                    if (!empty($ad_items)) {
                        $data['list_ad'][] = array(
                            'first' => $ad['first'] ?? 3,
                            'frequency' => $ad['frequency'] ?? 10,
                            'items' => $ad_items
                        );
                    }
                }
            }
        }

        // 底部菜单
        $bottom_menu = ZhuiGe_Xcx::option_value('idle_bottom_menu');
        $data['bottom_menu'] = array();
        if ($bottom_menu && is_array($bottom_menu)) {
            foreach ($bottom_menu as $menu) {
                if (!empty($menu['image']) && !empty($menu['title'])) {
                    $data['bottom_menu'][] = array(
                        'image' => ZhuiGe_Xcx::option_image_url($menu['image']),
                        'title' => $menu['title'],
                        'link' => $menu['link'] ?? ''
                    );
                }
            }
        }

        // 分享图片
        $share_img = ZhuiGe_Xcx::option_value('idle_share_img');
        if ($share_img) {
            $data['share_img'] = ZhuiGe_Xcx::option_image_url($share_img);
        }

        return $this->success($data);
    }

    /**
     * 最新商品列表
     */
    public function last()
    {
        $offset = $this->request->get_param('offset') ?: 0;
        $limit = 20;

        $args = array(
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => 'zhuige-idle-goods-option',
                    'value' => '"stick";b:1',
                    'compare' => 'LIKE'
                ),
                array(
                    'key' => 'zhuige-idle-goods-option',
                    'value' => '"stick"',
                    'compare' => 'NOT LIKE'
                ),
                array(
                    'key' => 'zhuige-idle-goods-option',
                    'compare' => 'NOT EXISTS'
                )
            )
        );

        $query = new WP_Query($args);
        $list = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $list[] = zhuige_idle_goods_format(get_post());
            }
            wp_reset_postdata();
        }

        $more = count($list) >= $limit ? 'more' : 'noMore';

        return $this->success(array(
            'list' => $list,
            'more' => $more
        ));
    }

    /**
     * 分类商品列表
     */
    public function cat()
    {
        $cat_id = $this->request->get_param('cat_id');
        $offset = $this->request->get_param('offset') ?: 0;
        $limit = 20;

        if (!$cat_id) {
            return $this->error('分类ID不能为空');
        }

        $args = array(
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'tax_query' => array(
                array(
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ),
            ),
        );

        $query = new WP_Query($args);
        $list = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $list[] = zhuige_idle_goods_format(get_post());
            }
            wp_reset_postdata();
        }

        $more = count($list) >= $limit ? 'more' : 'noMore';

        return $this->success(array(
            'list' => $list,
            'more' => $more
        ));
    }

    /**
     * 搜索商品列表
     */
    public function list()
    {
        $cat_id = $this->request->get_param('cat_id');
        $search = $this->request->get_param('search');
        $offset = $this->request->get_param('offset') ?: 0;
        $limit = 20;

        $args = array(
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
        );

        if ($cat_id) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ),
            );
        }

        if ($search) {
            $args['s'] = $search;
        }

        $query = new WP_Query($args);
        $list = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $list[] = zhuige_idle_goods_format(get_post());
            }
            wp_reset_postdata();
        }

        $more = count($list) >= $limit ? 'more' : 'noMore';

        return $this->success(array(
            'list' => $list,
            'more' => $more
        ));
    }

    /**
     * 分类列表
     */
    public function cats()
    {
        $categories = get_terms(array(
            'taxonomy' => 'idle_category',
            'hide_empty' => false,
            'parent' => 0,
        ));

        $cats = array();
        foreach ($categories as $category) {
            $category_options = get_term_meta($category->term_id, 'zhuige-idle-category-options', true);
            
            // 检查是否启用
            if ($category_options && isset($category_options['switch']) && !$category_options['switch']) {
                continue;
            }

            // 获取该分类下的商品
            $goods_query = new WP_Query(array(
                'post_type' => 'zhuige_idle_goods',
                'post_status' => 'publish',
                'posts_per_page' => 6,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'idle_category',
                        'field' => 'term_id',
                        'terms' => $category->term_id,
                    ),
                ),
            ));

            $goods_list = array();
            if ($goods_query->have_posts()) {
                while ($goods_query->have_posts()) {
                    $goods_query->the_post();
                    $goods_list[] = array(
                        'id' => get_the_ID(),
                        'thumb' => get_the_post_thumbnail_url(get_the_ID(), 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg'
                    );
                }
                wp_reset_postdata();
            }

            $cats[] = array(
                'id' => $category->term_id,
                'name' => $category->name,
                'count' => $category->count,
                'list' => $goods_list
            );
        }

        return $this->success(array(
            'cats' => $cats
        ));
    }

    /**
     * 商品详情
     */
    public function detail()
    {
        $goods_id = $this->request->get_param('goods_id');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods' || $post->post_status !== 'publish') {
            return $this->error('商品不存在');
        }

        // 增加浏览量
        $views = (int) get_post_meta($goods_id, 'zhuige_views', true);
        update_post_meta($goods_id, 'zhuige_views', $views + 1);

        $goods = zhuige_idle_goods_detail_format($post);

        return $this->success($goods);
    }

    /**
     * 发布设置
     */
    public function post_setting()
    {
        $data = array();

        // 发布权限检查
        $data['post_require_mobile'] = ZhuiGe_Xcx::option_value('idle_post_require_mobile') ? 1 : 0;
        $data['post_require_avatar'] = ZhuiGe_Xcx::option_value('idle_post_require_avatar') ? 1 : 0;
        $data['post_require_weixin'] = ZhuiGe_Xcx::option_value('idle_post_require_weixin') ? 1 : 0;

        // 发布协议
        $data['fbxy'] = ZhuiGe_Xcx::option_value('idle_fbxy') ?: '';

        // 分类列表
        $categories = get_terms(array(
            'taxonomy' => 'idle_category',
            'hide_empty' => false,
        ));

        $cats = array();
        foreach ($categories as $category) {
            $cats[] = array(
                'id' => $category->term_id,
                'name' => $category->name,
                'parent' => $category->parent
            );
        }
        $data['cats'] = $cats;

        return $this->success($data);
    }

    /**
     * 发布商品
     */
    public function post_goods()
    {
        // 检查用户登录
        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录');
        }

        // 检查发布权限
        if (ZhuiGe_Xcx::option_value('idle_post_require_mobile')) {
            if (!zhuige_xcx_is_set_mobile($user_id)) {
                return $this->error('请先绑定手机号', 'require_mobile');
            }
        }

        if (ZhuiGe_Xcx::option_value('idle_post_require_avatar')) {
            if (!zhuige_xcx_is_set_avatar($user_id)) {
                return $this->error('请先设置头像昵称', 'require_avatar');
            }
        }

        if (ZhuiGe_Xcx::option_value('idle_post_require_weixin')) {
            $weixin = get_user_meta($user_id, 'zhuige_weixin', true);
            if (!$weixin) {
                return $this->error('请先设置微信二维码', 'require_weixin');
            }
        }

        // 获取参数
        $title = $this->request->get_param('title');
        $content = $this->request->get_param('content');
        $price = $this->request->get_param('price');
        $cat_id = $this->request->get_param('cat_id');
        $images = $this->request->get_param('images');
        $goods_id = $this->request->get_param('goods_id'); // 编辑时传入

        // 验证参数
        if (!$title) {
            return $this->error('请输入商品标题');
        }
        if (!$content) {
            return $this->error('请输入商品描述');
        }
        if (!$price || !is_numeric($price)) {
            return $this->error('请输入正确的价格');
        }
        if (!$cat_id) {
            return $this->error('请选择商品分类');
        }
        if (!$images || !is_array($images) || empty($images)) {
            return $this->error('请上传商品图片');
        }

        // 创建或更新商品
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'post_author' => $user_id,
        );

        if ($goods_id) {
            // 编辑商品
            $existing_post = get_post($goods_id);
            if (!$existing_post || $existing_post->post_author != $user_id) {
                return $this->error('无权限编辑此商品');
            }
            $post_data['ID'] = $goods_id;
            $post_id = wp_update_post($post_data);
        } else {
            // 新建商品
            $post_id = wp_insert_post($post_data);
        }

        if (is_wp_error($post_id)) {
            return $this->error('发布失败');
        }

        // 设置分类
        wp_set_post_terms($post_id, array($cat_id), 'idle_category');

        // 保存商品选项
        $options = array(
            'price' => $price,
            'images' => $images,
        );
        update_post_meta($post_id, 'zhuige-idle-goods-option', $options);

        // 设置缩略图（第一张图片）
        if (!empty($images[0])) {
            $attachment_id = attachment_url_to_postid($images[0]);
            if ($attachment_id) {
                set_post_thumbnail($post_id, $attachment_id);
            }
        }

        return $this->success(array(
            'goods_id' => $post_id,
            'message' => $goods_id ? '编辑成功' : '发布成功'
        ));
    }

    /**
     * 获取编辑商品信息
     */
    public function edit_goods()
    {
        $goods_id = $this->request->get_param('goods_id');
        $user_id = get_current_user_id();

        if (!$user_id) {
            return $this->error('请先登录');
        }

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods' || $post->post_author != $user_id) {
            return $this->error('商品不存在或无权限编辑');
        }

        $options = get_post_meta($goods_id, 'zhuige-idle-goods-option', true);
        $categories = wp_get_post_terms($goods_id, 'idle_category');

        $data = array(
            'title' => $post->post_title,
            'content' => $post->post_content,
            'price' => $options['price'] ?? '0',
            'cat_id' => !empty($categories) ? $categories[0]->term_id : 0,
            'images' => $options['images'] ?? array(),
        );

        return $this->success($data);
    }
}
