<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 闲置物品API控制器
 */
class ZhuiGe_Xcx_Idle_Controller extends WP_REST_Controller
{
    protected $namespace;
    protected $rest_base;
    protected $request;

    public function __construct()
    {
        $this->namespace = 'zhuige-xcx/v1';
        $this->rest_base = 'idle';
    }

    /**
     * 注册路由
     */
    public function register_routes()
    {
        // 获取设置
        register_rest_route($this->namespace, '/' . $this->rest_base . '/setting', array(
            'methods' => 'POST',
            'callback' => array($this, 'setting'),
            'permission_callback' => '__return_true'
        ));

        // 商品列表
        register_rest_route($this->namespace, '/' . $this->rest_base . '/list', array(
            'methods' => 'POST',
            'callback' => array($this, 'list'),
            'permission_callback' => '__return_true'
        ));

        // 商品详情
        register_rest_route($this->namespace, '/' . $this->rest_base . '/detail', array(
            'methods' => 'POST',
            'callback' => array($this, 'detail'),
            'permission_callback' => '__return_true'
        ));

        // 分类列表
        register_rest_route($this->namespace, '/' . $this->rest_base . '/cats', array(
            'methods' => 'POST',
            'callback' => array($this, 'cats'),
            'permission_callback' => '__return_true'
        ));

        // 创建商品
        register_rest_route($this->namespace, '/' . $this->rest_base . '/create', array(
            'methods' => 'POST',
            'callback' => array($this, 'create'),
            'permission_callback' => array($this, 'check_user_permission')
        ));

        // 编辑商品
        register_rest_route($this->namespace, '/' . $this->rest_base . '/edit', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit'),
            'permission_callback' => array($this, 'check_user_permission')
        ));

        // 获取编辑商品信息
        register_rest_route($this->namespace, '/' . $this->rest_base . '/edit_goods', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_goods'),
            'permission_callback' => array($this, 'check_user_permission')
        ));
    }

    /**
     * 检查用户权限
     */
    public function check_user_permission($request)
    {
        return is_user_logged_in();
    }

    /**
     * 返回成功响应
     */
    protected function success($data = null, $message = '操作成功！')
    {
        return new WP_REST_Response(array(
            'code' => 0,
            'message' => $message,
            'data' => $data
        ), 200);
    }

    /**
     * 返回错误响应
     */
    protected function error($message = '操作失败！', $code = 1)
    {
        return new WP_REST_Response(array(
            'code' => $code,
            'message' => $message,
            'data' => null
        ), 200);
    }

    /**
     * 获取设置信息
     */
    public function setting($request)
    {
        $this->request = $request;

        $data = array(
            'background' => '',
            'slides' => array(),
            'icons' => array(),
            'nav_cats' => zhuige_idle_get_categories(),
            'list_ad' => array(),
            'bottom_menu' => array(),
            'share_img' => ''
        );

        return $this->success($data);
    }

    /**
     * 商品列表
     */
    public function list($request)
    {
        $this->request = $request;
        
        $cat_id = $request->get_param('cat_id');
        $search = $request->get_param('search');
        $offset = $request->get_param('offset') ?: 0;
        $limit = 20;

        $args = array(
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
        );

        if ($cat_id) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ),
            );
        }

        if ($search) {
            $args['s'] = $search;
        }

        $query = new WP_Query($args);
        $list = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $list[] = zhuige_idle_goods_format(get_post());
            }
            wp_reset_postdata();
        }

        $data = array(
            'list' => $list,
            'more' => (count($list) >= $limit ? 'more' : 'nomore')
        );

        return $this->success($data);
    }

    /**
     * 商品详情
     */
    public function detail($request)
    {
        $this->request = $request;
        
        $goods_id = $request->get_param('goods_id');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods' || $post->post_status !== 'publish') {
            return $this->error('商品不存在');
        }

        // 增加浏览量
        $views = (int) get_post_meta($goods_id, 'zhuige_views', true);
        update_post_meta($goods_id, 'zhuige_views', $views + 1);

        $goods = zhuige_idle_goods_detail_format($post);

        return $this->success($goods);
    }

    /**
     * 分类列表
     */
    public function cats($request)
    {
        $this->request = $request;

        $cats = zhuige_idle_get_categories(true);
        
        $data = array(
            'cats' => $cats,
            'fbxy' => '' // 发布协议链接
        );

        return $this->success($data);
    }

    /**
     * 创建商品
     */
    public function create($request)
    {
        $this->request = $request;
        
        $title = $request->get_param('title');
        $content = $request->get_param('content');
        $images = $request->get_param('images');
        $cat_id = $request->get_param('cat_id');
        $price = $request->get_param('price');

        if (!$title) {
            return $this->error('商品名称不能为空');
        }

        if (!$content) {
            return $this->error('商品描述不能为空');
        }

        if (!$cat_id) {
            return $this->error('请选择商品分类');
        }

        if (!$price || floatval($price) <= 0) {
            return $this->error('请输入正确的价格');
        }

        $current_user_id = get_current_user_id();
        
        // 创建文章
        $post_data = array(
            'post_title' => sanitize_text_field($title),
            'post_content' => wp_kses_post($content),
            'post_status' => 'publish',
            'post_type' => 'zhuige_idle_goods',
            'post_author' => $current_user_id
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            return $this->error('发布失败，请重试');
        }

        // 设置分类
        wp_set_post_terms($post_id, array($cat_id), 'idle_category');

        // 保存商品信息
        $options = array(
            'price' => sanitize_text_field($price),
            'original_price' => '',
            'condition_type' => 'good',
            'location' => '',
            'contact_info' => '',
            'trade_type' => 'both',
            'is_negotiable' => 1,
            'stick' => 0
        );
        update_post_meta($post_id, 'zhuige-idle-goods-option', $options);

        // 保存图片
        if ($images) {
            $image_list = json_decode($images, true);
            if (is_array($image_list) && !empty($image_list)) {
                // 设置第一张图片为特色图片
                $first_image = $image_list[0];
                if (isset($first_image) && is_string($first_image)) {
                    $attachment_id = $this->get_attachment_id_by_url($first_image);
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                    }
                }
                
                // 保存所有图片
                update_post_meta($post_id, 'zhuige_idle_images', $images);
            }
        }

        $data = array(
            'goods_id' => $post_id
        );

        return $this->success($data, '发布成功！');
    }

    /**
     * 编辑商品
     */
    public function edit($request)
    {
        $this->request = $request;
        
        $goods_id = $request->get_param('goods_id');
        $title = $request->get_param('title');
        $content = $request->get_param('content');
        $images = $request->get_param('images');
        $cat_id = $request->get_param('cat_id');
        $price = $request->get_param('price');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods') {
            return $this->error('商品不存在');
        }

        $current_user_id = get_current_user_id();
        if ($post->post_author != $current_user_id && !current_user_can('edit_others_posts')) {
            return $this->error('没有权限编辑此商品');
        }

        if (!$title) {
            return $this->error('商品名称不能为空');
        }

        if (!$content) {
            return $this->error('商品描述不能为空');
        }

        if (!$cat_id) {
            return $this->error('请选择商品分类');
        }

        if (!$price || floatval($price) <= 0) {
            return $this->error('请输入正确的价格');
        }

        // 更新文章
        $post_data = array(
            'ID' => $goods_id,
            'post_title' => sanitize_text_field($title),
            'post_content' => wp_kses_post($content)
        );

        $result = wp_update_post($post_data);

        if (is_wp_error($result)) {
            return $this->error('更新失败，请重试');
        }

        // 更新分类
        wp_set_post_terms($goods_id, array($cat_id), 'idle_category');

        // 更新商品信息
        $options = get_post_meta($goods_id, 'zhuige-idle-goods-option', true);
        if (!$options) {
            $options = array();
        }
        $options['price'] = sanitize_text_field($price);
        update_post_meta($goods_id, 'zhuige-idle-goods-option', $options);

        // 更新图片
        if ($images) {
            $image_list = json_decode($images, true);
            if (is_array($image_list) && !empty($image_list)) {
                // 设置第一张图片为特色图片
                $first_image = $image_list[0];
                if (isset($first_image) && is_string($first_image)) {
                    $attachment_id = $this->get_attachment_id_by_url($first_image);
                    if ($attachment_id) {
                        set_post_thumbnail($goods_id, $attachment_id);
                    }
                }
                
                // 保存所有图片
                update_post_meta($goods_id, 'zhuige_idle_images', $images);
            }
        }

        return $this->success(null, '更新成功！');
    }

    /**
     * 获取编辑商品信息
     */
    public function edit_goods($request)
    {
        $this->request = $request;
        
        $goods_id = $request->get_param('goods_id');

        if (!$goods_id) {
            return $this->error('商品ID不能为空');
        }

        $post = get_post($goods_id);
        if (!$post || $post->post_type !== 'zhuige_idle_goods') {
            return $this->error('商品不存在');
        }

        $current_user_id = get_current_user_id();
        if ($post->post_author != $current_user_id && !current_user_can('edit_others_posts')) {
            return $this->error('没有权限编辑此商品');
        }

        $options = get_post_meta($goods_id, 'zhuige-idle-goods-option', true);
        $images_json = get_post_meta($goods_id, 'zhuige_idle_images', true);
        $images = array();
        
        if ($images_json) {
            $image_list = json_decode($images_json, true);
            if (is_array($image_list)) {
                $images = $image_list;
            }
        }

        // 获取分类
        $categories = wp_get_post_terms($goods_id, 'idle_category');
        $cat_id = 0;
        if (!empty($categories)) {
            $cat_id = $categories[0]->term_id;
        }

        $data = array(
            'title' => $post->post_title,
            'content' => $post->post_content,
            'price' => $options['price'] ?? '0',
            'cat_id' => $cat_id,
            'images' => $images
        );

        return $this->success($data);
    }

    /**
     * 根据URL获取附件ID
     */
    private function get_attachment_id_by_url($url)
    {
        global $wpdb;
        
        $attachment = $wpdb->get_col($wpdb->prepare("SELECT ID FROM {$wpdb->posts} WHERE guid='%s';", $url));
        
        if (!empty($attachment)) {
            return $attachment[0];
        }
        
        return false;
    }
}

// 注册API路由
add_action('rest_api_init', function () {
    $controller = new ZhuiGe_Xcx_Idle_Controller();
    $controller->register_routes();
});
