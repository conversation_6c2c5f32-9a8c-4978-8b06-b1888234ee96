<?php

if (!defined('ABSPATH')) {
    exit;
}

// 注册闲置物品文章类型
function zhuige_idle_register_post_type() {
    $labels = array(
        'name'               => '闲置物品',
        'singular_name'      => '闲置物品',
        'menu_name'          => '闲置物品',
        'add_new'            => '添加商品',
        'add_new_item'       => '添加新商品',
        'edit_item'          => '编辑商品',
        'new_item'           => '新商品',
        'view_item'          => '查看商品',
        'search_items'       => '搜索商品',
        'not_found'          => '未找到商品',
        'not_found_in_trash' => '回收站中未找到商品',
    );

    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'idle-goods'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => 25,
        'menu_icon'           => 'dashicons-store',
        'supports'            => array('title', 'editor', 'thumbnail', 'excerpt', 'author'),
        'taxonomies'          => array('idle_category', 'idle_tag'),
    );

    register_post_type('zhuige_idle_goods', $args);
}
add_action('init', 'zhuige_idle_register_post_type');

// 注册分类法
function zhuige_idle_register_taxonomies() {
    // 商品分类
    $category_labels = array(
        'name'              => '商品分类',
        'singular_name'     => '商品分类',
        'search_items'      => '搜索分类',
        'all_items'         => '所有分类',
        'parent_item'       => '父级分类',
        'parent_item_colon' => '父级分类:',
        'edit_item'         => '编辑分类',
        'update_item'       => '更新分类',
        'add_new_item'      => '添加新分类',
        'new_item_name'     => '新分类名称',
        'menu_name'         => '商品分类',
    );

    register_taxonomy('idle_category', array('zhuige_idle_goods'), array(
        'hierarchical'      => true,
        'labels'            => $category_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'idle-category'),
    ));

    // 商品标签
    $tag_labels = array(
        'name'                       => '商品标签',
        'singular_name'              => '商品标签',
        'search_items'               => '搜索标签',
        'popular_items'              => '热门标签',
        'all_items'                  => '所有标签',
        'edit_item'                  => '编辑标签',
        'update_item'                => '更新标签',
        'add_new_item'               => '添加新标签',
        'new_item_name'              => '新标签名称',
        'separate_items_with_commas' => '用逗号分隔标签',
        'add_or_remove_items'        => '添加或删除标签',
        'choose_from_most_used'      => '从常用标签中选择',
        'not_found'                  => '未找到标签',
        'menu_name'                  => '商品标签',
    );

    register_taxonomy('idle_tag', array('zhuige_idle_goods'), array(
        'hierarchical'          => false,
        'labels'                => $tag_labels,
        'show_ui'               => true,
        'show_admin_column'     => true,
        'update_count_callback' => '_update_post_term_count',
        'query_var'             => true,
        'rewrite'               => array('slug' => 'idle-tag'),
    ));
}
add_action('init', 'zhuige_idle_register_taxonomies');

// 为商品添加自定义字段
if (class_exists('CSF')) {
    $idle_goods_option = 'zhuige-idle-goods-option';
    CSF::createMetabox($idle_goods_option, array(
        'title'        => '商品设置',
        'post_type'    => 'zhuige_idle_goods',
    ));

    CSF::createSection($idle_goods_option, array(
        'fields' => array(

            array(
                'id'    => 'price',
                'type'  => 'text',
                'title' => '商品价格',
                'subtitle' => '请输入商品价格（数字）',
                'default' => '0',
            ),

            array(
                'id'      => 'images',
                'type'    => 'gallery',
                'title'   => '商品图片',
                'subtitle' => '上传商品图片，第一张为封面图',
            ),

            array(
                'id'    => 'stick',
                'type'  => 'switcher',
                'title' => '推广',
                'subtitle' => '是否设为推广商品',
                'default' => false,
            ),

            array(
                'id'    => 'comment_switch',
                'type'  => 'switcher',
                'title' => '评论开关',
                'subtitle' => '是否允许用户评论此商品',
                'default' => true,
            ),

            array(
                'id'    => 'comment_require_mobile',
                'type'  => 'switcher',
                'title' => '评论需要手机号',
                'subtitle' => '评论时是否需要用户绑定手机号',
                'default' => false,
                'dependency' => array('comment_switch', '==', true),
            ),

            array(
                'id'    => 'comment_require_avatar',
                'type'  => 'switcher',
                'title' => '评论需要头像',
                'subtitle' => '评论时是否需要用户设置头像',
                'default' => false,
                'dependency' => array('comment_switch', '==', true),
            ),

        )
    ));

    // 为分类添加自定义字段
    $category_options = 'zhuige-idle-category-options';
    CSF::createTaxonomyOptions($category_options, array(
        'taxonomy'  => 'idle_category',
    ));

    CSF::createSection($category_options, array(
        'fields' => array(
            array(
                'id'      => 'cover',
                'type'    => 'media',
                'title'   => '分类封面',
                'library' => 'image',
            ),

            array(
                'id'    => 'switch',
                'type'  => 'switcher',
                'title' => '启用',
                'subtitle' => '是否在分类列表中显示',
                'default' => true,
            ),
        )
    ));

    // 为标签添加自定义字段
    $tag_options = 'zhuige-idle-tag-options';
    CSF::createTaxonomyOptions($tag_options, array(
        'taxonomy'  => 'idle_tag',
    ));

    CSF::createSection($tag_options, array(
        'fields' => array(
            array(
                'id'      => 'color',
                'type'    => 'color',
                'title'   => '标签颜色',
                'default' => '#2c70db',
            ),

            array(
                'id'    => 'switch',
                'type'  => 'switcher',
                'title' => '启用',
                'subtitle' => '是否在标签列表中显示',
                'default' => true,
            ),
        )
    ));
}

// 添加自定义列到商品列表
function zhuige_idle_add_custom_columns($columns) {
    $new_columns = array();
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        if ($key == 'title') {
            $new_columns['price'] = '价格';
            $new_columns['stick'] = '推广';
        }
    }
    return $new_columns;
}
add_filter('manage_zhuige_idle_goods_posts_columns', 'zhuige_idle_add_custom_columns');

// 显示自定义列内容
function zhuige_idle_custom_column_content($column, $post_id) {
    switch ($column) {
        case 'price':
            $price = get_post_meta($post_id, 'zhuige-idle-goods-option', true);
            echo $price && isset($price['price']) ? '￥' . $price['price'] : '￥0';
            break;
        case 'stick':
            $options = get_post_meta($post_id, 'zhuige-idle-goods-option', true);
            echo ($options && isset($options['stick']) && $options['stick']) ? '是' : '否';
            break;
    }
}
add_action('manage_zhuige_idle_goods_posts_custom_column', 'zhuige_idle_custom_column_content', 10, 2);
