<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 创建闲置物品相关数据表
 */
function zhuige_idle_create_tables()
{
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // 创建闲置物品扩展信息表
    $table_name = $wpdb->prefix . 'zhuige_idle_goods_meta';
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        post_id bigint(20) NOT NULL,
        price decimal(10,2) NOT NULL DEFAULT '0.00',
        original_price decimal(10,2) NOT NULL DEFAULT '0.00',
        condition_type varchar(20) NOT NULL DEFAULT 'good',
        location varchar(255) NOT NULL DEFAULT '',
        contact_info text,
        trade_type varchar(20) NOT NULL DEFAULT 'both',
        is_negotiable tinyint(1) NOT NULL DEFAULT 1,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY post_id (post_id),
        KEY price (price),
        KEY condition_type (condition_type),
        KEY trade_type (trade_type)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // 创建交易记录表
    $table_name = $wpdb->prefix . 'zhuige_idle_trades';
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        goods_id bigint(20) NOT NULL,
        buyer_id bigint(20) NOT NULL,
        seller_id bigint(20) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'pending',
        message text,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY goods_id (goods_id),
        KEY buyer_id (buyer_id),
        KEY seller_id (seller_id),
        KEY status (status)
    ) $charset_collate;";

    dbDelta($sql);

    // 创建收藏表
    $table_name = $wpdb->prefix . 'zhuige_idle_favorites';
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        goods_id bigint(20) NOT NULL,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_goods (user_id, goods_id),
        KEY user_id (user_id),
        KEY goods_id (goods_id)
    ) $charset_collate;";

    dbDelta($sql);
}

/**
 * 删除闲置物品相关数据表
 */
function zhuige_idle_drop_tables()
{
    global $wpdb;

    $tables = [
        $wpdb->prefix . 'zhuige_idle_goods_meta',
        $wpdb->prefix . 'zhuige_idle_trades',
        $wpdb->prefix . 'zhuige_idle_favorites'
    ];

    foreach ($tables as $table) {
        $wpdb->query("DROP TABLE IF EXISTS $table");
    }
}

/**
 * 插件激活时执行
 */
function zhuige_idle_activate()
{
    zhuige_idle_create_tables();
    
    // 刷新重写规则
    flush_rewrite_rules();
}

/**
 * 插件停用时执行
 */
function zhuige_idle_deactivate()
{
    // 刷新重写规则
    flush_rewrite_rules();
}

/**
 * 插件卸载时执行
 */
function zhuige_idle_uninstall()
{
    // 删除数据表
    zhuige_idle_drop_tables();
    
    // 删除相关选项
    delete_option('zhuige_idle_settings');
    
    // 删除所有闲置物品文章
    $posts = get_posts([
        'post_type' => 'zhuige_idle_goods',
        'numberposts' => -1,
        'post_status' => 'any'
    ]);
    
    foreach ($posts as $post) {
        wp_delete_post($post->ID, true);
    }
    
    // 删除分类法
    $terms = get_terms([
        'taxonomy' => 'idle_category',
        'hide_empty' => false
    ]);
    
    foreach ($terms as $term) {
        wp_delete_term($term->term_id, 'idle_category');
    }
}
