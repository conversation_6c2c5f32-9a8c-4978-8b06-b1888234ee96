<?php {
    // 闲置物品推广记录表
    $table_zhuige_idle_promotions = $wpdb->prefix . 'zhuige_idle_promotions';
    $idle_promotions_sql = "CREATE TABLE IF NOT EXISTS `$table_zhuige_idle_promotions` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `goods_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商品ID',
        `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
        `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推广金额',
        `days` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '推广天数',
        `start_time` int(10) UNSIGNED DEFAULT '0' COMMENT '开始时间',
        `end_time` int(10) UNSIGNED DEFAULT '0' COMMENT '结束时间',
        `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active,expired)',
        `time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `goods_id` (`goods_id`),
        KEY `user_id` (`user_id`),
        KEY `status` (`status`)
    ) $charset_collate;";
    dbDelta($idle_promotions_sql);
}
