<?php

/**
 * 追格小程序 - 闲置物品功能函数
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 闲置物品格式化（兼容现有代码）
 */
if (!function_exists('zhuige_idle_shop_goods_format')) {
    function zhuige_idle_shop_goods_format($post)
    {
        $item = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'excerpt' => wp_trim_words($post->post_content, 20, '...'),
            'content' => $post->post_content,
            'time' => get_the_time('Y-m-d H:i:s', $post->ID),
            'views' => (int) get_post_meta($post->ID, 'zhuige_views', true),
        ];

        // 获取扩展信息
        $options = get_post_meta($post->ID, 'zhuige_idle_goods_options', true);
        if (!$options) {
            $options = [];
        }

        $item['price'] = $options['price'] ?? '0';
        $item['original_price'] = $options['original_price'] ?? '0';
        $item['condition_type'] = $options['condition_type'] ?? 'good';
        $item['location'] = $options['location'] ?? '';
        $item['contact_info'] = $options['contact_info'] ?? '';
        $item['trade_type'] = $options['trade_type'] ?? 'both';
        $item['is_negotiable'] = ($options['is_negotiable'] ?? 1) ? 1 : 0;

        // 获取缩略图
        $item['thumb'] = get_the_post_thumbnail_url($post->ID, 'medium');
        if (!$item['thumb']) {
            $item['thumb'] = ZhuiGe_Xcx::option_image_url('', 'placeholder.jpg');
        }

        // 获取图片列表
        $item['images'] = zhuige_idle_get_post_images($post->ID);

        // 获取分类
        $categories = wp_get_post_terms($post->ID, 'idle_category');
        $item['cat'] = null;
        if (!empty($categories)) {
            $item['cat'] = [
                'id' => $categories[0]->term_id,
                'name' => $categories[0]->name,
                'slug' => $categories[0]->slug
            ];
        }

        // 获取标签
        $tags = wp_get_post_terms($post->ID, 'post_tag');
        $item['tags'] = [];
        foreach ($tags as $tag) {
            $item['tags'][] = [
                'id' => $tag->term_id,
                'name' => $tag->name,
                'slug' => $tag->slug
            ];
        }

        // 获取用户信息
        $author = get_userdata($post->post_author);
        $item['user'] = [
            'user_id' => $author->ID,
            'nickname' => $author->display_name,
            'avatar' => get_avatar_url($author->ID, ['size' => 96]),
            'weixin' => get_user_meta($author->ID, 'weixin_qrcode', true)
        ];

        // 置顶标识
        $item['stick'] = get_post_meta($post->ID, 'zhuige_idle_goods_stick', true) ? 1 : 0;

        // 统计信息
        $item['comment_count'] = get_comments_number($post->ID);
        $item['like_count'] = (int) get_post_meta($post->ID, 'zhuige_like_count', true);
        $item['favorite_count'] = (int) get_post_meta($post->ID, 'zhuige_favorite_count', true);

        return $item;
    }
}

/**
 * 获取文章图片列表
 */
if (!function_exists('zhuige_idle_get_post_images')) {
    function zhuige_idle_get_post_images($post_id)
    {
        $images = [];
        
        // 获取特色图片
        $thumbnail_url = get_the_post_thumbnail_url($post_id, 'large');
        if ($thumbnail_url) {
            $images[] = $thumbnail_url;
        }
        
        // 获取附加图片
        $gallery_images = get_post_meta($post_id, 'zhuige_idle_images', true);
        if ($gallery_images) {
            $gallery_images = json_decode($gallery_images, true);
            if (is_array($gallery_images)) {
                $images = array_merge($images, $gallery_images);
            }
        }
        
        return $images;
    }
}

/**
 * 获取闲置物品分类列表
 */
if (!function_exists('zhuige_idle_get_categories')) {
    function zhuige_idle_get_categories($include_count = false)
    {
        $categories = get_terms([
            'taxonomy' => 'idle_category',
            'hide_empty' => false,
            'orderby' => 'count',
            'order' => 'DESC'
        ]);
        
        $cat_list = [];
        foreach ($categories as $category) {
            $cat_item = [
                'id' => $category->term_id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description
            ];
            
            if ($include_count) {
                $cat_item['count'] = $category->count;
                
                // 获取该分类下的商品示例
                $posts = get_posts([
                    'post_type' => 'zhuige_idle_goods',
                    'post_status' => 'publish',
                    'posts_per_page' => 4,
                    'tax_query' => [
                        [
                            'taxonomy' => 'idle_category',
                            'field' => 'term_id',
                            'terms' => $category->term_id
                        ]
                    ]
                ]);
                
                $cat_item['list'] = [];
                foreach ($posts as $post) {
                    $cat_item['list'][] = [
                        'id' => $post->ID,
                        'title' => $post->post_title,
                        'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZhuiGe_Xcx::option_image_url('', 'placeholder.jpg')
                    ];
                }
            }
            
            $cat_list[] = $cat_item;
        }
        
        return $cat_list;
    }
}

/**
 * 闲置物品详情格式化
 */
if (!function_exists('zhuige_idle_goods_detail_format')) {
    function zhuige_idle_goods_detail_format($post)
    {
        $current_user_id = get_current_user_id();
        $item = zhuige_idle_shop_goods_format($post);
        
        // 获取作者详细信息
        $author = get_userdata($post->post_author);
        $item['author'] = [
            'user_id' => $author->ID,
            'nickname' => $author->display_name,
            'avatar' => get_avatar_url($author->ID, ['size' => 96]),
            'weixin' => get_user_meta($author->ID, 'weixin_qrcode', true),
            'is_follow' => 0
        ];
        
        // 检查是否已关注
        if ($current_user_id && function_exists('zhuige_check_user_follow')) {
            $item['author']['is_follow'] = zhuige_check_user_follow($current_user_id, $author->ID) ? 1 : 0;
        }
        
        // 获取点赞用户列表
        $item['like_list'] = [];
        if (function_exists('zhuige_get_post_like_users')) {
            $like_users = zhuige_get_post_like_users($post->ID, 6);
            foreach ($like_users as $user) {
                $item['like_list'][] = [
                    'user_id' => $user->ID,
                    'avatar' => get_avatar_url($user->ID, ['size' => 48])
                ];
            }
        }
        
        // 获取推荐商品
        $item['recs'] = [];
        $item['rec_cat'] = $item['cat'];
        if ($item['cat']) {
            $rec_posts = get_posts([
                'post_type' => 'zhuige_idle_goods',
                'post_status' => 'publish',
                'posts_per_page' => 4,
                'post__not_in' => [$post->ID],
                'tax_query' => [
                    [
                        'taxonomy' => 'idle_category',
                        'field' => 'term_id',
                        'terms' => $item['cat']['id']
                    ]
                ],
                'orderby' => 'rand'
            ]);
            
            foreach ($rec_posts as $rec_post) {
                $rec_options = get_post_meta($rec_post->ID, 'zhuige_idle_goods_options', true);
                $item['recs'][] = [
                    'id' => $rec_post->ID,
                    'title' => $rec_post->post_title,
                    'thumb' => get_the_post_thumbnail_url($rec_post->ID, 'medium') ?: ZhuiGe_Xcx::option_image_url('', 'placeholder.jpg'),
                    'price' => $rec_options['price'] ?? '0'
                ];
            }
        }
        
        // 用户互动状态
        $item['is_like'] = 0;
        $item['is_comment'] = 0;
        $item['is_favorite'] = 0;
        $item['is_show_promotion'] = 0;
        $item['is_show_edit'] = ($current_user_id == $post->post_author) ? 1 : 0;
        
        if ($current_user_id) {
            if (function_exists('zhuige_check_user_like')) {
                $item['is_like'] = zhuige_check_user_like($current_user_id, $post->ID) ? 1 : 0;
            }
            if (function_exists('zhuige_check_user_favorite')) {
                $item['is_favorite'] = zhuige_check_user_favorite($current_user_id, $post->ID) ? 1 : 0;
            }
            
            $item['is_comment'] = 1;
            
            if ($current_user_id == $post->post_author && function_exists('zhuige_promotion_enabled')) {
                $item['is_show_promotion'] = 1;
            }
        }

        return $item;
    }
}
