<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 格式化闲置物品列表数据
 */
function zhuige_idle_goods_format($post)
{
    if (!$post) {
        return null;
    }

    $options = get_post_meta($post->ID, 'zhuige-idle-goods-option', true);
    $author = get_userdata($post->post_author);
    
    // 获取分类信息
    $categories = wp_get_post_terms($post->ID, 'idle_category');
    $cat = null;
    if (!empty($categories)) {
        $cat = array(
            'id' => $categories[0]->term_id,
            'name' => $categories[0]->name,
            'slug' => $categories[0]->slug
        );
    }
    
    // 获取标签信息
    $tags = wp_get_post_terms($post->ID, 'post_tag');
    $tag_list = array();
    foreach ($tags as $tag) {
        $tag_list[] = array(
            'id' => $tag->term_id,
            'name' => $tag->name,
            'slug' => $tag->slug
        );
    }
    
    // 获取用户信息
    $user_info = array(
        'user_id' => $author->ID,
        'nickname' => $author->display_name,
        'avatar' => get_avatar_url($author->ID, array('size' => 96)),
        'weixin' => get_user_meta($author->ID, 'weixin_qrcode', true)
    );
    
    $item = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'excerpt' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
        'content' => $post->post_content,
        'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
        'images' => zhuige_idle_get_post_images($post->ID),
        'time' => get_the_time('Y-m-d H:i:s', $post->ID),
        'views' => (int) get_post_meta($post->ID, 'zhuige_views', true),
        'price' => $options['price'] ?? '0',
        'original_price' => $options['original_price'] ?? '0',
        'condition_type' => $options['condition_type'] ?? 'good',
        'location' => $options['location'] ?? '',
        'contact_info' => $options['contact_info'] ?? '',
        'trade_type' => $options['trade_type'] ?? 'both',
        'is_negotiable' => ($options && isset($options['is_negotiable']) && $options['is_negotiable']) ? 1 : 0,
        'stick' => ($options && isset($options['stick']) && $options['stick']) ? 1 : 0,
        'cat' => $cat,
        'tags' => $tag_list,
        'user' => $user_info,
        'comment_count' => get_comments_number($post->ID),
        'like_count' => (int) get_post_meta($post->ID, 'zhuige_like_count', true),
        'favorite_count' => (int) get_post_meta($post->ID, 'zhuige_favorite_count', true)
    );

    return $item;
}

/**
 * 格式化闲置物品详情数据
 */
function zhuige_idle_goods_detail_format($post)
{
    if (!$post) {
        return null;
    }

    $options = get_post_meta($post->ID, 'zhuige-idle-goods-option', true);
    $current_user_id = get_current_user_id();
    $author = get_userdata($post->post_author);
    
    // 获取分类信息
    $categories = wp_get_post_terms($post->ID, 'idle_category');
    $cat = null;
    if (!empty($categories)) {
        $cat = array(
            'id' => $categories[0]->term_id,
            'name' => $categories[0]->name,
            'slug' => $categories[0]->slug
        );
    }
    
    // 获取标签信息
    $tags = wp_get_post_terms($post->ID, 'post_tag');
    $tag_list = array();
    foreach ($tags as $tag) {
        $tag_list[] = array(
            'id' => $tag->term_id,
            'name' => $tag->name,
            'slug' => $tag->slug
        );
    }
    
    // 获取作者详细信息
    $author_info = array(
        'user_id' => $author->ID,
        'nickname' => $author->display_name,
        'avatar' => get_avatar_url($author->ID, array('size' => 96)),
        'weixin' => get_user_meta($author->ID, 'weixin_qrcode', true),
        'is_follow' => 0
    );
    
    // 检查是否已关注
    if ($current_user_id && function_exists('zhuige_check_user_follow')) {
        $author_info['is_follow'] = zhuige_check_user_follow($current_user_id, $author->ID) ? 1 : 0;
    }
    
    // 获取点赞用户列表
    $like_list = array();
    if (function_exists('zhuige_get_post_like_users')) {
        $like_users = zhuige_get_post_like_users($post->ID, 6);
        foreach ($like_users as $user) {
            $like_list[] = array(
                'user_id' => $user->ID,
                'avatar' => get_avatar_url($user->ID, array('size' => 48))
            );
        }
    }
    
    // 获取推荐商品
    $recs = array();
    if ($cat) {
        $rec_posts = get_posts(array(
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => 4,
            'post__not_in' => array($post->ID),
            'tax_query' => array(
                array(
                    'taxonomy' => 'idle_category',
                    'field' => 'term_id',
                    'terms' => $cat['id']
                )
            ),
            'orderby' => 'rand'
        ));
        
        foreach ($rec_posts as $rec_post) {
            $rec_options = get_post_meta($rec_post->ID, 'zhuige-idle-goods-option', true);
            $recs[] = array(
                'id' => $rec_post->ID,
                'title' => $rec_post->post_title,
                'thumb' => get_the_post_thumbnail_url($rec_post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
                'price' => $rec_options['price'] ?? '0'
            );
        }
    }
    
    $item = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'content' => $post->post_content,
        'excerpt' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
        'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
        'images' => zhuige_idle_get_post_images($post->ID),
        'time' => get_the_time('Y-m-d H:i:s', $post->ID),
        'views' => (int) get_post_meta($post->ID, 'zhuige_views', true),
        'price' => $options['price'] ?? '0',
        'original_price' => $options['original_price'] ?? '0',
        'condition_type' => $options['condition_type'] ?? 'good',
        'location' => $options['location'] ?? '',
        'contact_info' => $options['contact_info'] ?? '',
        'trade_type' => $options['trade_type'] ?? 'both',
        'is_negotiable' => ($options && isset($options['is_negotiable']) && $options['is_negotiable']) ? 1 : 0,
        'stick' => ($options && isset($options['stick']) && $options['stick']) ? 1 : 0,
        'tags' => $tag_list,
        'author' => $author_info,
        'rec_cat' => $cat,
        'recs' => $recs,
        'like_list' => $like_list,
        'comment_count' => get_comments_number($post->ID),
        'like_count' => (int) get_post_meta($post->ID, 'zhuige_like_count', true),
        'favorite_count' => (int) get_post_meta($post->ID, 'zhuige_favorite_count', true),
        'is_like' => 0,
        'is_comment' => 0,
        'is_favorite' => 0,
        'is_show_promotion' => 0,
        'is_show_edit' => ($current_user_id == $post->post_author) ? 1 : 0
    );
    
    // 检查用户互动状态
    if ($current_user_id) {
        if (function_exists('zhuige_check_user_like')) {
            $item['is_like'] = zhuige_check_user_like($current_user_id, $post->ID) ? 1 : 0;
        }
        if (function_exists('zhuige_check_user_favorite')) {
            $item['is_favorite'] = zhuige_check_user_favorite($current_user_id, $post->ID) ? 1 : 0;
        }
        
        // 检查是否有评论权限
        $item['is_comment'] = 1;
        
        // 检查是否显示推广按钮
        if ($current_user_id == $post->post_author && function_exists('zhuige_promotion_enabled')) {
            $item['is_show_promotion'] = 1;
        }
    }

    return $item;
}

/**
 * 获取文章图片列表
 */
function zhuige_idle_get_post_images($post_id)
{
    $images = array();
    
    // 获取特色图片
    $thumbnail_url = get_the_post_thumbnail_url($post_id, 'large');
    if ($thumbnail_url) {
        $images[] = $thumbnail_url;
    }
    
    // 获取附加图片
    $gallery_images = get_post_meta($post_id, 'zhuige_idle_images', true);
    if ($gallery_images) {
        $gallery_images = json_decode($gallery_images, true);
        if (is_array($gallery_images)) {
            $images = array_merge($images, $gallery_images);
        }
    }
    
    return $images;
}

/**
 * 兼容函数：为了与现有代码保持一致
 */
function zhuige_idle_shop_goods_format($post)
{
    return zhuige_idle_goods_format($post);
}

/**
 * 获取闲置物品分类列表
 */
function zhuige_idle_get_categories($include_count = false)
{
    $categories = get_terms(array(
        'taxonomy' => 'idle_category',
        'hide_empty' => false,
        'orderby' => 'count',
        'order' => 'DESC'
    ));
    
    $cat_list = array();
    foreach ($categories as $category) {
        $cat_item = array(
            'id' => $category->term_id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description
        );
        
        if ($include_count) {
            $cat_item['count'] = $category->count;
            
            // 获取该分类下的商品示例
            $posts = get_posts(array(
                'post_type' => 'zhuige_idle_goods',
                'post_status' => 'publish',
                'posts_per_page' => 4,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'idle_category',
                        'field' => 'term_id',
                        'terms' => $category->term_id
                    )
                )
            ));
            
            $cat_item['list'] = array();
            foreach ($posts as $post) {
                $cat_item['list'][] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title,
                    'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg'
                );
            }
        }
        
        $cat_list[] = $cat_item;
    }
    
    return $cat_list;
}
