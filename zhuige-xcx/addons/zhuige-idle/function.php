<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 格式化商品列表数据
 */
function zhuige_idle_goods_format($post)
{
    if (!$post) {
        return null;
    }

    $options = get_post_meta($post->ID, 'zhuige-idle-goods-option', true);
    
    $item = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'excerpt' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
        'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
        'time' => get_the_time('Y-m-d H:i:s', $post->ID),
        'views' => (int) get_post_meta($post->ID, 'zhuige_views', true),
        'price' => $options['price'] ?? '0',
        'stick' => ($options && isset($options['stick']) && $options['stick']) ? 1 : 0,
    );

    // 用户信息
    $author = get_userdata($post->post_author);
    if ($author) {
        $item['user'] = array(
            'user_id' => $author->ID,
            'nickname' => zhuige_xcx_get_user_nickname($author->ID),
            'avatar' => zhuige_xcx_get_user_avatar($author->ID),
        );
    }

    // 商品标签
    $tags = wp_get_post_terms($post->ID, 'idle_tag');
    $item['tags'] = array();
    if ($tags && !is_wp_error($tags)) {
        foreach ($tags as $tag) {
            $tag_options = get_term_meta($tag->term_id, 'zhuige-idle-tag-options', true);
            if (!$tag_options || !isset($tag_options['switch']) || $tag_options['switch']) {
                $item['tags'][] = array(
                    'id' => $tag->term_id,
                    'name' => $tag->name,
                    'color' => $tag_options['color'] ?? '#2c70db'
                );
            }
        }
    }

    // 商品分类
    $categories = wp_get_post_terms($post->ID, 'idle_category');
    if ($categories && !is_wp_error($categories) && !empty($categories)) {
        $category = $categories[0];
        $item['cat'] = array(
            'id' => $category->term_id,
            'name' => $category->name
        );
    }

    return $item;
}

/**
 * 格式化商品详情数据
 */
function zhuige_idle_goods_detail_format($post)
{
    if (!$post) {
        return null;
    }

    $options = get_post_meta($post->ID, 'zhuige-idle-goods-option', true);
    $current_user_id = get_current_user_id();
    
    $item = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'content' => $post->post_content,
        'excerpt' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
        'thumb' => get_the_post_thumbnail_url($post->ID, 'medium') ?: ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
        'time' => get_the_time('Y-m-d H:i:s', $post->ID),
        'views' => (int) get_post_meta($post->ID, 'zhuige_views', true),
        'price' => $options['price'] ?? '0',
        'stick' => ($options && isset($options['stick']) && $options['stick']) ? 1 : 0,
    );

    // 商品图片
    $item['images'] = array();
    if ($options && isset($options['images']) && is_array($options['images'])) {
        foreach ($options['images'] as $image) {
            $item['images'][] = array(
                'image' => $image,
                'link' => ''
            );
        }
    } else {
        // 如果没有设置图片，使用缩略图
        $item['images'][] = array(
            'image' => $item['thumb'],
            'link' => ''
        );
    }

    // 作者信息
    $author = get_userdata($post->post_author);
    if ($author) {
        $item['author'] = array(
            'user_id' => $author->ID,
            'nickname' => zhuige_xcx_get_user_nickname($author->ID),
            'avatar' => zhuige_xcx_get_user_avatar($author->ID),
            'sign' => get_user_meta($author->ID, 'zhuige_sign', true) ?: '',
            'weixin' => get_user_meta($author->ID, 'zhuige_weixin', true) ?: '',
            'is_follow' => false,
        );

        // 检查是否关注
        if ($current_user_id) {
            global $wpdb;
            $follow_table = $wpdb->prefix . 'zhuige_xcx_user_follow';
            $is_follow = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $follow_table WHERE user_id = %d AND follow_user_id = %d",
                $current_user_id, $author->ID
            ));
            $item['author']['is_follow'] = $is_follow > 0;
        }

        // 用户认证信息
        if (function_exists('zhuige_xcx_certify_is_certify')) {
            $certify = zhuige_xcx_certify_is_certify($author->ID);
            if ($certify && isset($certify['status']) && $certify['status']) {
                $item['author']['certify'] = $certify;
            }
        }
    }

    // 商品标签
    $tags = wp_get_post_terms($post->ID, 'idle_tag');
    $item['tags'] = array();
    if ($tags && !is_wp_error($tags)) {
        foreach ($tags as $tag) {
            $tag_options = get_term_meta($tag->term_id, 'zhuige-idle-tag-options', true);
            if (!$tag_options || !isset($tag_options['switch']) || $tag_options['switch']) {
                $item['tags'][] = array(
                    'id' => $tag->term_id,
                    'name' => $tag->name,
                    'color' => $tag_options['color'] ?? '#2c70db'
                );
            }
        }
    }

    // 推荐分类（当前商品的分类）
    $categories = wp_get_post_terms($post->ID, 'idle_category');
    if ($categories && !is_wp_error($categories) && !empty($categories)) {
        $category = $categories[0];
        $item['rec_cat'] = array(
            'id' => $category->term_id,
            'name' => $category->name
        );
    }

    // 评论设置
    $global_comment_switch = ZhuiGe_Xcx::option_value('idle_comment_switch');
    $post_comment_switch = $options['comment_switch'] ?? true;
    $item['comment_switch'] = ($global_comment_switch && $post_comment_switch) ? 1 : 0;
    
    // 评论数量
    $item['comment_count'] = wp_count_comments($post->ID)->approved;

    // 动态评论权限检查
    if ($item['comment_switch'] && $current_user_id) {
        // 评论是否要求头像昵称
        $global_require_avatar = ZhuiGe_Xcx::option_value('idle_comment_require_avatar');
        $post_require_avatar = $options['comment_require_avatar'] ?? false;
        if ($global_require_avatar || $post_require_avatar) {
            if (!zhuige_xcx_is_set_avatar($current_user_id)) {
                $item['comment_require_avatar2'] = 1;
            }
        }

        // 评论是否要求手机号
        $global_require_mobile = ZhuiGe_Xcx::option_value('idle_comment_require_mobile');
        $post_require_mobile = $options['comment_require_mobile'] ?? false;
        if ($global_require_mobile || $post_require_mobile) {
            if (!zhuige_xcx_is_set_mobile($current_user_id)) {
                $item['comment_require_mobile2'] = 1;
            }
        }
    }

    return $item;
}

/**
 * 获取相关商品
 */
function zhuige_idle_get_related_goods($goods_id, $limit = 4)
{
    $post = get_post($goods_id);
    if (!$post) {
        return array();
    }

    // 获取当前商品的分类
    $categories = wp_get_post_terms($goods_id, 'idle_category');
    $category_ids = array();
    if ($categories && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            $category_ids[] = $category->term_id;
        }
    }

    $args = array(
        'post_type' => 'zhuige_idle_goods',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'post__not_in' => array($goods_id),
        'orderby' => 'rand',
    );

    if (!empty($category_ids)) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'idle_category',
                'field' => 'term_id',
                'terms' => $category_ids,
            ),
        );
    }

    $query = new WP_Query($args);
    $goods = array();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $goods[] = zhuige_idle_goods_format(get_post());
        }
        wp_reset_postdata();
    }

    return $goods;
}
