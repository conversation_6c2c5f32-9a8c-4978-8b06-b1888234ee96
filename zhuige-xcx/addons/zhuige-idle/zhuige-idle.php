<?php
/**
 * Plugin Name: 追格闲置物品
 * Description: 微信小程序闲置物品模块
 * Version: 1.0.0
 * Author: 追格科技
 */

if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('ZHUIGE_IDLE_VERSION', '1.0.0');
define('ZHUIGE_IDLE_PATH', plugin_dir_path(__FILE__));
define('ZHUIGE_IDLE_URL', plugin_dir_url(__FILE__));

// 主类
class ZhuiGe_Idle
{
    private static $instance = null;

    public static function instance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function __construct()
    {
        $this->init();
    }

    private function init()
    {
        // 加载文件
        $this->includes();
        
        // 注册钩子
        $this->hooks();
        
        // 注册API路由
        $this->register_routes();
    }

    private function includes()
    {
        require_once ZHUIGE_IDLE_PATH . 'post-type.php';
        require_once ZHUIGE_IDLE_PATH . 'function.php';
        require_once ZHUIGE_IDLE_PATH . 'goods-controller.php';
        
        // 只在后台加载管理界面
        if (is_admin()) {
            require_once ZHUIGE_IDLE_PATH . 'admin.php';
        }
    }

    private function hooks()
    {
        // 激活插件时创建数据表
        register_activation_hook(__FILE__, array($this, 'activate'));
        
        // 停用插件时清理
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    private function register_routes()
    {
        add_action('rest_api_init', function () {
            // 首页设置
            register_rest_route('zhuige/v1', '/idle/setting', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'setting'),
                'permission_callback' => '__return_true',
            ));

            // 最新商品列表
            register_rest_route('zhuige/v1', '/idle/last', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'last'),
                'permission_callback' => '__return_true',
            ));

            // 分类商品列表
            register_rest_route('zhuige/v1', '/idle/cat', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'cat'),
                'permission_callback' => '__return_true',
            ));

            // 搜索商品列表
            register_rest_route('zhuige/v1', '/idle/list', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'list'),
                'permission_callback' => '__return_true',
            ));

            // 分类列表
            register_rest_route('zhuige/v1', '/idle/cats', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'cats'),
                'permission_callback' => '__return_true',
            ));

            // 商品详情
            register_rest_route('zhuige/v1', '/idle/detail', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'detail'),
                'permission_callback' => '__return_true',
            ));

            // 发布设置
            register_rest_route('zhuige/v1', '/idle/post_setting', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'post_setting'),
                'permission_callback' => '__return_true',
            ));

            // 发布商品
            register_rest_route('zhuige/v1', '/idle/post_goods', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'post_goods'),
                'permission_callback' => '__return_true',
            ));

            // 获取编辑商品信息
            register_rest_route('zhuige/v1', '/idle/edit_goods', array(
                'methods' => 'POST',
                'callback' => array(new ZhuiGe_Xcx_Idle_Goods_Controller(), 'edit_goods'),
                'permission_callback' => '__return_true',
            ));
        });
    }

    public function activate()
    {
        // 刷新重写规则
        flush_rewrite_rules();
    }

    public function deactivate()
    {
        // 刷新重写规则
        flush_rewrite_rules();
    }
}

// 初始化插件
function zhuige_idle_init()
{
    return ZhuiGe_Idle::instance();
}

// 启动插件
zhuige_idle_init();
