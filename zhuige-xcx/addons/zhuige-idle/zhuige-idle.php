<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Plugin Name: 追格小程序 - 闲置物品
 * Plugin URI: https://www.zhuige.com/
 * Description: 闲置物品交易模块，支持商品发布、分类管理、交易沟通等功能
 * Version: 1.0.0
 * Author: 追格
 * Author URI: https://www.zhuige.com/
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: zhuige-idle
 * Domain Path: /languages
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('ZHUIGE_IDLE_VERSION', '1.0.0');
define('ZHUIGE_IDLE_PLUGIN_FILE', __FILE__);
define('ZHUIGE_IDLE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ZHUIGE_IDLE_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * 主插件类
 */
class ZhuiGe_Xcx_Idle
{
    /**
     * 单例实例
     */
    private static $instance = null;

    /**
     * 获取单例实例
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct()
    {
        $this->init_hooks();
    }

    /**
     * 初始化钩子
     */
    private function init_hooks()
    {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // 激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * 初始化
     */
    public function init()
    {
        // 检查依赖
        if (!$this->check_dependencies()) {
            return;
        }

        // 加载核心文件
        $this->load_includes();
    }

    /**
     * 检查依赖
     */
    private function check_dependencies()
    {
        // 检查是否安装了追格小程序主插件
        if (!class_exists('ZhuiGe_Xcx')) {
            add_action('admin_notices', array($this, 'dependency_notice'));
            return false;
        }

        return true;
    }

    /**
     * 依赖提醒
     */
    public function dependency_notice()
    {
        echo '<div class="notice notice-error"><p>';
        echo '闲置物品模块需要先安装并激活追格小程序主插件。';
        echo '</p></div>';
    }

    /**
     * 加载包含文件
     */
    private function load_includes()
    {
        // 加载核心功能文件
        require_once ZHUIGE_IDLE_PLUGIN_DIR . 'function.php';
        require_once ZHUIGE_IDLE_PLUGIN_DIR . 'post-type.php';
        require_once ZHUIGE_IDLE_PLUGIN_DIR . 'goods-controller.php';

        // 后台管理
        if (is_admin()) {
            require_once ZHUIGE_IDLE_PLUGIN_DIR . 'admin.php';
        }
    }

    /**
     * 加载语言文件
     */
    public function load_textdomain()
    {
        load_plugin_textdomain(
            'zhuige-idle',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    /**
     * 插件激活
     */
    public function activate()
    {
        // 创建数据表
        if (function_exists('zhuige_idle_activate')) {
            zhuige_idle_activate();
        }

        // 创建默认分类
        $this->create_default_categories();

        // 刷新重写规则
        flush_rewrite_rules();
    }

    /**
     * 插件停用
     */
    public function deactivate()
    {
        if (function_exists('zhuige_idle_deactivate')) {
            zhuige_idle_deactivate();
        }
    }

    /**
     * 创建默认分类
     */
    private function create_default_categories()
    {
        $default_categories = array(
            '数码产品' => '手机、电脑、相机等数码设备',
            '家居用品' => '家具、家电、装饰品等',
            '服装鞋包' => '衣服、鞋子、包包等',
            '图书音像' => '书籍、CD、DVD等',
            '运动户外' => '运动器材、户外用品等',
            '母婴用品' => '婴儿用品、玩具等',
            '美妆护肤' => '化妆品、护肤品等',
            '其他物品' => '其他各类闲置物品'
        );

        foreach ($default_categories as $name => $description) {
            if (!term_exists($name, 'idle_category')) {
                wp_insert_term($name, 'idle_category', array(
                    'description' => $description
                ));
            }
        }
    }

    /**
     * 获取插件版本
     */
    public function get_version()
    {
        return ZHUIGE_IDLE_VERSION;
    }

    /**
     * 获取插件目录路径
     */
    public function get_plugin_dir()
    {
        return ZHUIGE_IDLE_PLUGIN_DIR;
    }

    /**
     * 获取插件URL
     */
    public function get_plugin_url()
    {
        return ZHUIGE_IDLE_PLUGIN_URL;
    }
}

/**
 * 获取主插件实例
 */
function zhuige_idle()
{
    return ZhuiGe_Xcx_Idle::get_instance();
}

// 初始化插件
zhuige_idle();
