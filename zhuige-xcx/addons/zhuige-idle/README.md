# 追格小程序 - 闲置物品模块

## 模块简介

闲置物品模块是追格小程序的一个功能扩展，允许用户发布、浏览和交易二手闲置物品。该模块提供了完整的商品管理、分类管理、用户交互等功能。

## 主要功能

### 前端功能
- **商品浏览**: 瀑布流展示商品，支持分类筛选
- **商品详情**: 详细的商品信息展示，包括图片、价格、描述等
- **商品发布**: 用户可以发布自己的闲置物品
- **商品编辑**: 用户可以编辑自己发布的商品
- **分类浏览**: 按分类查看商品
- **搜索功能**: 支持关键词搜索商品
- **用户交互**: 点赞、收藏、评论等社交功能
- **联系卖家**: 通过微信二维码联系卖家

### 后端功能
- **商品管理**: 后台管理所有商品信息
- **分类管理**: 管理商品分类
- **用户权限**: 控制发布权限和浏览权限
- **审核机制**: 支持自动审核或人工审核
- **数据统计**: 商品浏览量、点赞数等统计

## 技术实现

### 数据结构
- **自定义文章类型**: `zhuige_idle_goods`
- **自定义分类法**: `idle_category`
- **扩展字段**: 价格、成色、联系方式等

### API接口
- `GET /idle/setting` - 获取模块设置
- `POST /idle/list` - 获取商品列表
- `POST /idle/detail` - 获取商品详情
- `POST /idle/cats` - 获取分类列表
- `POST /idle/create` - 创建商品
- `POST /idle/edit` - 编辑商品
- `POST /idle/edit_goods` - 获取编辑商品信息

### 前端页面
- `/pages/idle-shop/index/index` - 首页
- `/pages/idle-shop/detail/detail` - 详情页
- `/pages/idle-shop/post/post` - 发布页
- `/pages/idle-shop/list/list` - 列表页
- `/pages/idle-shop/classify/classify` - 分类页

## 安装配置

### 1. 模块激活
在WordPress后台的"追格小程序"插件管理中激活"闲置物品"模块。

### 2. 基本设置
进入"闲置物品" -> "闲置设置"进行基本配置：
- 启用/禁用模块
- 设置页面标题和描述
- 配置用户权限
- 设置图片上传限制
- 配置发布协议

### 3. 分类管理
在"闲置物品" -> "闲置分类"中管理商品分类：
- 添加新分类
- 编辑分类信息
- 设置分类层级

### 4. 小程序配置
在小程序的`pages.json`中已自动添加相关页面配置。

## 使用说明

### 用户发布商品
1. 进入闲置物品首页
2. 点击发布按钮
3. 填写商品信息（标题、描述、价格等）
4. 上传商品图片
5. 选择商品分类
6. 提交发布

### 用户浏览商品
1. 在首页浏览推荐商品
2. 通过分类筛选商品
3. 使用搜索功能查找商品
4. 点击商品进入详情页

### 用户交互
1. 点赞和收藏感兴趣的商品
2. 评论商品
3. 通过微信二维码联系卖家
4. 关注商品发布者

## 数据库表结构

### zhuige_idle_goods_meta
存储商品扩展信息：
- `post_id` - 商品ID
- `price` - 售价
- `original_price` - 原价
- `condition_type` - 成色
- `location` - 所在地
- `contact_info` - 联系方式
- `trade_type` - 交易方式
- `is_negotiable` - 是否可议价

### zhuige_idle_trades
存储交易记录：
- `goods_id` - 商品ID
- `buyer_id` - 买家ID
- `seller_id` - 卖家ID
- `status` - 交易状态
- `message` - 交易留言

### zhuige_idle_favorites
存储收藏记录：
- `user_id` - 用户ID
- `goods_id` - 商品ID

## 兼容性说明

该模块与现有的追格小程序框架完全兼容，包括：
- 用户系统
- 评论系统
- 点赞收藏系统
- 消息通知系统
- 搜索系统

## 版本历史

### v1.0.0
- 初始版本发布
- 基本的商品发布和浏览功能
- 分类管理功能
- 用户交互功能
- 后台管理功能

## 技术支持

如有问题请联系追格技术支持团队。
